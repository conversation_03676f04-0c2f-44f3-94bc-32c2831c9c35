import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: modelCompletion
    
    property string message: "模型切换成功"
    
    // 信号定义
    signal navigateToHome()
    
    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Label {
                text: "模型切换完成"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }
        }
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 30

        Item {
            Layout.fillHeight: true
        }

        // 成功图标
        Rectangle {
            Layout.alignment: Qt.AlignHCenter
            width: 150
            height: 150
            color: "#4caf50"
            radius: 75

            Text {
                anchors.centerIn: parent
                text: "✓"
                font.pixelSize: 80
                color: "white"
                font.bold: true
            }
        }

        Label {
            text: "模型加载完成！"
            font.pixelSize: 36
            font.bold: true
            color: "#4caf50"
            Layout.alignment: Qt.AlignHCenter
        }

        Rectangle {
            Layout.fillWidth: true
            Layout.maximumWidth: 700
            Layout.alignment: Qt.AlignHCenter
            height: 140
            color: "#f0f8ff"
            radius: 12
            border.color: "#2196f3"
            border.width: 2

            ColumnLayout {
                anchors.centerIn: parent
                width: parent.width - 50
                spacing: 15

                Label {
                    text: "当前使用的模型:"
                    font.pixelSize: 18
                    color: "#333333"
                }

                Label {
                    text: modelManager.currentModel
                    font.pixelSize: 24
                    font.bold: true
                    color: "#2196f3"
                }

                Label {
                    text: modelManager.getModelDescription(modelManager.currentModel)
                    font.pixelSize: 16
                    color: "#333333"
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }
            }
        }

        Label {
            text: message
            font.pixelSize: 20
            color: "#cccccc"
            Layout.alignment: Qt.AlignHCenter
        }

        Button {
            text: "返回主页"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 200
            Layout.preferredHeight: 60
            onClicked: modelCompletion.navigateToHome()

            background: Rectangle {
                color: parent.pressed ? "#1976d2" : "#2196f3"
                radius: 8
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 20
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }

        Item {
            Layout.fillHeight: true
        }
    }
}
