#ifndef MONITORWINDOW_H
#define MONITORWINDOW_H

#include <QObject>
#include <QTimer>
#include "monitoring_datasource.h"

class MonitorWindow : public QObject
{
    Q_OBJECT
    Q_PROPERTY(MonitoringDataSource* dataSource READ dataSource CONSTANT)

public:
    explicit MonitorWindow(QObject *parent = nullptr);

    MonitoringDataSource* dataSource() const { return m_dataSource; }

public slots:
    void startMonitoring();
    void stopMonitoring();
    void clearAllData();
    void switchBoilerAsync(const QString &boilerName);

private:
    MonitoringDataSource *m_dataSource;
};

#endif // MONITORWINDOW_H
