#include "systemtray.h"
#include <QQmlApplicationEngine>
#include <QQuickWindow>
#include <QIcon>
#include <QMessageBox>
#include <QDebug>
#include <QStyle>
#include <QApplication>

SystemTray::SystemTray(QObject *parent)
    : QObject(parent)
    , m_trayIcon(nullptr)
    , m_trayMenu(nullptr)
    , m_showAction(nullptr)
    , m_quitAction(nullptr)
    , m_qmlEngine(nullptr)
{
    // 检查系统是否支持系统托盘
    if (!QSystemTrayIcon::isSystemTrayAvailable()) {
        qWarning() << "系统托盘不可用";
        return;
    }

    createTrayIcon();
    createTrayMenu();
    
    // 连接托盘图标激活信号
    connect(m_trayIcon, &QSystemTrayIcon::activated,
            this, &SystemTray::onTrayIconActivated);
}

SystemTray::~SystemTray()
{
    if (m_trayIcon) {
        m_trayIcon->hide();
    }
}

bool SystemTray::isSystemTrayAvailable() const
{
    return QSystemTrayIcon::isSystemTrayAvailable();
}

bool SystemTray::isVisible() const
{
    return m_trayIcon ? m_trayIcon->isVisible() : false;
}

void SystemTray::setVisible(bool visible)
{
    if (m_trayIcon && m_trayIcon->isVisible() != visible) {
        m_trayIcon->setVisible(visible);
        emit visibilityChanged();
    }
}

void SystemTray::setQmlEngine(QQmlApplicationEngine *engine)
{
    m_qmlEngine = engine;
}

void SystemTray::showMainWindow()
{
    if (m_qmlEngine) {
        QObject *rootObject = m_qmlEngine->rootObjects().first();
        if (rootObject) {
            QQuickWindow *window = qobject_cast<QQuickWindow*>(rootObject);
            if (window) {
                window->show();
                window->raise();
                window->requestActivate();
                qDebug() << "主窗口已显示";
            }
        }
    }
    emit showWindowRequested();
}

void SystemTray::hideToTray()
{
    if (m_qmlEngine) {
        QObject *rootObject = m_qmlEngine->rootObjects().first();
        if (rootObject) {
            QQuickWindow *window = qobject_cast<QQuickWindow*>(rootObject);
            if (window) {
                window->hide();
                qDebug() << "主窗口已隐藏到系统托盘";
            }
        }
    }
    
    // 显示托盘提示消息
    if (m_trayIcon) {
        showTrayMessage("华润电力湖北有限公司锅炉智慧燃烧系统", 
                       "应用程序已最小化到系统托盘，双击托盘图标可重新显示窗口");
    }
}

void SystemTray::quitApplication()
{
    qDebug() << "正在退出应用程序...";
    emit quitRequested();
    QApplication::quit();
}

void SystemTray::showTrayMessage(const QString &title, const QString &message)
{
    if (m_trayIcon && m_trayIcon->isVisible()) {
        m_trayIcon->showMessage(title, message, QSystemTrayIcon::Information, 3000);
    }
}

void SystemTray::onTrayIconActivated(QSystemTrayIcon::ActivationReason reason)
{
    switch (reason) {
    case QSystemTrayIcon::DoubleClick:
        showMainWindow();
        break;
    case QSystemTrayIcon::Trigger:
        emit trayIconActivated();
        break;
    default:
        break;
    }
}

void SystemTray::createTrayIcon()
{
    m_trayIcon = new QSystemTrayIcon(this);
    
    // 设置托盘图标（使用应用程序图标）
    QIcon icon(":/images/app-icon.ico");
    if (icon.isNull()) {
        // 如果ico文件不存在，尝试使用png文件
        icon = QIcon(":/images/app-icon.png");
    }
    
    if (!icon.isNull()) {
        m_trayIcon->setIcon(icon);
    } else {
        // 如果都没有，使用系统默认图标
        m_trayIcon->setIcon(QApplication::style()->standardIcon(QStyle::SP_ComputerIcon));
        qWarning() << "未找到应用程序图标，使用默认图标";
    }
    
    m_trayIcon->setToolTip("华润电力湖北有限公司锅炉智慧燃烧系统");
}

void SystemTray::createTrayMenu()
{
    m_trayMenu = new QMenu();
    
    // 创建菜单项
    m_showAction = new QAction("显示主窗口", this);
    connect(m_showAction, &QAction::triggered, this, &SystemTray::showMainWindow);
    
    m_quitAction = new QAction("退出程序", this);
    connect(m_quitAction, &QAction::triggered, this, &SystemTray::quitApplication);
    
    // 添加菜单项
    m_trayMenu->addAction(m_showAction);
    m_trayMenu->addSeparator();
    m_trayMenu->addAction(m_quitAction);
    
    // 设置托盘菜单
    if (m_trayIcon) {
        m_trayIcon->setContextMenu(m_trayMenu);
    }
}
