#ifndef SMOKE_ANALYZER_COMM_H
#define SMOKE_ANALYZER_COMM_H

// #ifdef __cplusplus
// extern "C" {
// #endif
#include "config_manager.h"
#include <unordered_map>
#include "boiler.h"

#define INVALID_DATA 999999

// 根据构建类型自动设置DEBUG（避免与系统预定义冲突）
#ifndef DEBUG
    #ifdef QT_DEBUG
        #define DEBUG 1
    #else
        #define DEBUG 0
    #endif
#endif

// 外部变量声明
extern std::unordered_map<std::string, Boiler*> boiler_map;
extern ConfigManager* g_config_manager;

// 全局串口访问互斥锁，解决COM3端口共享问题
extern std::mutex g_serial_port_mutex;

std::unordered_map<std::string, int>  start_fd(ConfigManager *config_manager);
std::unordered_map<std::string, Boiler *>  get_boiler_list(ConfigManager *config_manager);
void get_realtime_data(std::string boiler_name,float *pco,float *po2,float *pcurrent,float *pvoltage,float *ptemperature,int *pswitch1);

// 调试输出函数，支持中文
void debug_printf(const char* format, ...);

// 错误输出函数，支持中文
void debug_perror(const char* msg);

// 清理串口缓冲区，避免DCS数据干扰烟气分析仪
void clear_serial_buffer(int fd);

int modbus_request(int fd, unsigned char slave_addr, unsigned char func_code,
                   unsigned short start_addr, unsigned short quantity, unsigned char *response);
int read_holding_registers(int fd, unsigned char slave_addr,
                           unsigned short start_addr, unsigned short quantity,
                           unsigned int *registers,bool is32bit);
unsigned short calculate_crc(unsigned char *buf, int len);
float hex_to_float(uint32_t hex_value);

// 压力表数据解析函数
float parse_pressure_data(uint16_t raw_data);



  // #ifdef __cplusplus
// }
// #endif

#endif // SMOKE_ANALYZER_COMM_H
