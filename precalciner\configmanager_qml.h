#ifndef CONFIGMANAGER_QML_H
#define CONFIGMANAGER_QML_H

#include <QObject>
#include <QVariantMap>
#include <QString>
#include <QStringList>
#include "config_manager.h"

// QML配置管理器包装类
class ConfigManagerQML : public QObject
{
    Q_OBJECT

public:
    explicit ConfigManagerQML(QObject *parent = nullptr);
    
    // 设置配置管理器实例
    void setConfigManager(ConfigManager* manager);

    // QML可调用的方法
    Q_INVOKABLE QVariantMap getRS485Config();
    Q_INVOKABLE QVariantMap getDCSRS485Config();
    Q_INVOKABLE QVariantMap getDCSOPCConfig();
    Q_INVOKABLE QVariantMap getParameterAdjustmentConfig();
    Q_INVOKABLE bool saveRS485Config(const QVariantMap& config);
    Q_INVOKABLE bool saveDCSRS485Config(const QVariantMap& config);
    Q_INVOKABLE bool saveDCSOPCConfig(const QVariantMap& config);
    Q_INVOKABLE bool saveParameterAdjustmentConfig(const QVariantMap& config);
    Q_INVOKABLE bool saveAllConfigs();
    Q_INVOKABLE bool reloadConfigs();
    Q_INVOKABLE bool restartApplication();
    Q_INVOKABLE void setConfigValue(const QString& section, const QString& key, const QString& value);
    Q_INVOKABLE QString getSmokeAnalyzerDeviceName();

signals:
    void configChanged();
    void configSaved(bool success);
    void configReloaded(bool success);

private:
    ConfigManager* m_configManager;
    
    // 辅助方法
    QVariantMap sectionToVariantMap(const std::string& sectionName);
    bool variantMapToSection(const std::string& sectionName, const QVariantMap& map);
};

#endif // CONFIGMANAGER_QML_H
