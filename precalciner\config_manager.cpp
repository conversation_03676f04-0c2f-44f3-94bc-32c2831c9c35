#include "config_manager.h"
#include <mutex>
#include <cstdio>  // for std::rename, std::remove

// 去除字符串两端空白
std::string ConfigManager::trim(const std::string& str) {
    const std::string whitespace = " \t\r\n";
    size_t start = str.find_first_not_of(whitespace);
    if (start == std::string::npos) return "";

    size_t end = str.find_last_not_of(whitespace);
    return str.substr(start, end - start + 1);
}

// 解析配置行
bool ConfigManager::parse_line(const std::string& line, std::string& section, std::string& key, std::string& value) {
    std::string trimmed = trim(line);

    // 空行或注释
    if (trimmed.empty() || trimmed[0] == ';' || trimmed[0] == '#') {
        return false;
    }

    // 节标记
    if (trimmed[0] == '[' && trimmed.back() == ']') {
        section = trim(trimmed.substr(1, trimmed.length() - 2));
        key.clear();
        value.clear();
        return true;
    }

    // 键值对
    size_t eq_pos = trimmed.find('=');
    if (eq_pos != std::string::npos) {
        key = trim(trimmed.substr(0, eq_pos));
        std::string value_part = trimmed.substr(eq_pos + 1);

        // 处理行尾注释
        size_t comment_pos = value_part.find(';');
        if (comment_pos != std::string::npos) {
            value = trim(value_part.substr(0, comment_pos));
        } else {
            value = trim(value_part);
        }
        return true;
    }

    return false;
}

// 构造函数
ConfigManager::ConfigManager(const std::string& path) : file_path(path) {
    // 确保目录存在
    size_t last_slash = file_path.find_last_of("/\\");
    if (last_slash != std::string::npos) {
        std::string dir = file_path.substr(0, last_slash);
        if (!dir.empty()) {
#ifdef _WIN32
            _mkdir(dir.c_str());
#else
            mkdir(dir.c_str(), 0755);
#endif
        }
    }
}

// 读取配置文件
bool ConfigManager::load() {
    std::lock_guard<std::mutex> lock(rw_mutex);

    std::ifstream file(file_path);
    if (!file.is_open()) {
        std::cerr << "Failed to open config file: " << file_path << std::endl;
        return false;
    }

    // 清空现有数据
    config_data.clear();

    std::string line;
    std::string current_section;

    while (std::getline(file, line)) {
        std::string section, key, value;
        if (parse_line(line, section, key, value)) {
            if (!section.empty()) {
                // 新的节
                current_section = section;
            } else if (!current_section.empty() && !key.empty()) {
                // 键值对
                config_data[current_section][key] = value;
            }
        }
    }

    file.close();
    return true;
}

// 保存配置文件 - 保留原始格式和注释
bool ConfigManager::save() {
    // 保存当前要更新的数据
    auto sections_to_save = config_data;

    // 获取锁进行保存操作
    std::lock_guard<std::mutex> lock(rw_mutex);

    // 读取原始文件内容
    std::ifstream input_file(file_path);
    if (!input_file.is_open()) {
        std::cerr << "Failed to open config file for reading: " << file_path << std::endl;
        return false;
    }

    std::vector<std::string> file_lines;
    std::string line;
    while (std::getline(input_file, line)) {
        file_lines.push_back(line);
    }
    input_file.close();

    // 创建临时文件
    std::string temp_file_path = file_path + ".tmp";
    std::ofstream output_file(temp_file_path);
    if (!output_file.is_open()) {
        std::cerr << "Failed to create temporary file: " << temp_file_path << std::endl;
        return false;
    }

    // 逐行处理，更新需要修改的键值对
    std::string current_section;
    for (const auto& file_line : file_lines) {
        std::string section, key, value;
        bool is_parsed = parse_line(file_line, section, key, value);

        if (is_parsed && !section.empty()) {
            // 这是一个节标记
            current_section = section;
            output_file << file_line << "\n";
        } else if (is_parsed && !current_section.empty() && !key.empty()) {
            // 这是一个键值对，检查是否需要更新
            auto section_it = sections_to_save.find(current_section);
            if (section_it != sections_to_save.end()) {
                auto key_it = section_it->second.find(key);
                if (key_it != section_it->second.end()) {
                    // 需要更新这个键值对
                    output_file << key << " = " << key_it->second << "\n";
                    // 从待保存列表中移除已处理的键
                    section_it->second.erase(key_it);
                    if (section_it->second.empty()) {
                        sections_to_save.erase(section_it);
                    }
                } else {
                    // 保持原样
                    output_file << file_line << "\n";
                }
            } else {
                // 保持原样
                output_file << file_line << "\n";
            }
        } else {
            // 注释行、空行或其他内容，保持原样
            output_file << file_line << "\n";
        }
    }

    // 添加新的节和键值对（如果有的话）
    for (const auto& section_pair : sections_to_save) {
        const std::string& section_name = section_pair.first;
        if (!section_pair.second.empty()) {
            output_file << "\n[" << section_name << "]\n";
            for (const auto& key_value : section_pair.second) {
                output_file << key_value.first << " = " << key_value.second << "\n";
            }
        }
    }

    output_file.close();

    // 替换原文件 - 更安全的方法
    // 先备份原文件
    std::string backup_file_path = file_path + ".bak";
    std::remove(backup_file_path.c_str()); // 删除旧备份（如果存在）

    if (std::rename(file_path.c_str(), backup_file_path.c_str()) != 0) {
        std::cerr << "Failed to backup original config file" << std::endl;
        std::remove(temp_file_path.c_str()); // 清理临时文件
        return false;
    }

    // 将临时文件重命名为目标文件
    if (std::rename(temp_file_path.c_str(), file_path.c_str()) != 0) {
        std::cerr << "Failed to replace original config file" << std::endl;
        // 尝试恢复备份
        std::rename(backup_file_path.c_str(), file_path.c_str());
        std::remove(temp_file_path.c_str()); // 清理临时文件
        return false;
    }

    // 成功后删除备份文件
    std::remove(backup_file_path.c_str());

    return true;
}



// 检查配置项是否存在
bool ConfigManager::exists(const std::string& section, const std::string& key) const {
    std::lock_guard<std::mutex> lock(rw_mutex);

    auto sec_it = config_data.find(section);
    if (sec_it == config_data.end()) {
        return false;
    }

    return sec_it->second.find(key) != sec_it->second.end();
}

// 删除配置项
bool ConfigManager::remove(const std::string& section, const std::string& key) {
    std::lock_guard<std::mutex> lock(rw_mutex);

    auto sec_it = config_data.find(section);
    if (sec_it == config_data.end()) {
        return false;
    }

    auto key_it = sec_it->second.find(key);
    if (key_it == sec_it->second.end()) {
        return false;
    }

    sec_it->second.erase(key_it);

    // 如果节为空，删除整个节
    if (sec_it->second.empty()) {
        config_data.erase(sec_it);
    }

    return true;
}

// 删除整个节
bool ConfigManager::remove_section(const std::string& section) {
    std::lock_guard<std::mutex> lock(rw_mutex);

    auto sec_it = config_data.find(section);
    if (sec_it == config_data.end()) {
        return false;
    }

    config_data.erase(sec_it);
    return true;
}

// 获取所有节名
std::vector<std::string> ConfigManager::get_sections() const {
    std::lock_guard<std::mutex> lock(rw_mutex);

    std::vector<std::string> sections;
    sections.reserve(config_data.size());

    for (const auto& pair : config_data) {
        sections.push_back(pair.first);
    }

    return sections;
}

// 获取指定节中的所有键
std::vector<std::string> ConfigManager::get_keys(const std::string& section) const {
    std::lock_guard<std::mutex> lock(rw_mutex);

    auto sec_it = config_data.find(section);
    if (sec_it == config_data.end()) {
        return {};
    }

    std::vector<std::string> keys;
    keys.reserve(sec_it->second.size());

    for (const auto& pair : sec_it->second) {
        keys.push_back(pair.first);
    }

    return keys;
}

// 重载流操作符，用于调试
std::ostream& operator<<(std::ostream& os, const ConfigManager& config) {
    std::lock_guard<std::mutex> lock(config.rw_mutex);

    os << "Configuration from " << config.file_path << ":\n";

    for (const auto& section_pair : config.config_data) {
        const std::string& section = section_pair.first;
        os << "[" << section << "]\n";

        for (const auto& key_value_pair : section_pair.second) {
            const std::string& key = key_value_pair.first;
            const std::string& value = key_value_pair.second;
            os << "  " << key << " = " << value << "\n";
        }
    }

    return os;
}
