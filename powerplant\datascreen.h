#ifndef DATASCREEN_H
#define DATASCREEN_H

#include <QObject>
#include <QString>
#include <QTimer>
#include <QStringList>
#include <QDateTime>

class DataScreen : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString message READ message WRITE setMessage NOTIFY messageChanged)

    // 大屏实时数据属性
    Q_PROPERTY(QString payload READ payload NOTIFY dataChanged)
    Q_PROPERTY(QString mainSteamPressure READ mainSteamPressure NOTIFY dataChanged)
    Q_PROPERTY(QString mainSteamTemp READ mainSteamTemp NOTIFY dataChanged)
    Q_PROPERTY(QString reheatSteamPressure READ reheatSteamPressure NOTIFY dataChanged)
    Q_PROPERTY(QString reheatSteamTemp READ reheatSteamTemp NOTIFY dataChanged)
    Q_PROPERTY(QString mainSteamFlow READ mainSteamFlow NOTIFY dataChanged)
    Q_PROPERTY(QString totalFuel READ totalFuel NOTIFY dataChanged)
    Q_PROPERTY(QString totalAir READ totalAir NOTIFY dataChanged)
    Q_PROPERTY(QString oxygenContent READ oxygenContent NOTIFY dataChanged)
    Q_PROPERTY(QString furnacePressure READ furnacePressure NOTIFY dataChanged)

    // 烟气分析数据属性 - 采用与datasource相同的方式
    Q_PROPERTY(QString oxygenContent READ oxygenContent NOTIFY dataChanged)
    Q_PROPERTY(QString coContent READ coContent NOTIFY dataChanged)
    Q_PROPERTY(QString noxContent READ noxContent NOTIFY dataChanged)
    Q_PROPERTY(QString so2Content READ so2Content NOTIFY dataChanged)

    // DCS风机数据属性
    Q_PROPERTY(QString primaryFanA READ primaryFanA NOTIFY dataChanged)
    Q_PROPERTY(QString primaryFanB READ primaryFanB NOTIFY dataChanged)
    Q_PROPERTY(QString fanA READ fanA NOTIFY dataChanged)
    Q_PROPERTY(QString fanB READ fanB NOTIFY dataChanged)
    Q_PROPERTY(QString inducedFanA READ inducedFanA NOTIFY dataChanged)
    Q_PROPERTY(QString inducedFanB READ inducedFanB NOTIFY dataChanged)

    // 注释：数据大屏只需要实时显示，不需要数据列表缓存
    Q_PROPERTY(bool isRunning READ isRunning WRITE setIsRunning NOTIFY isRunningChanged)

    // 硬件数据相关属性
    Q_PROPERTY(QStringList boilerList READ boilerList NOTIFY boilerListChanged)
    Q_PROPERTY(QString currentBoiler READ currentBoiler WRITE setCurrentBoiler NOTIFY currentBoilerChanged)
    Q_PROPERTY(QStringList dcsList READ dcsList NOTIFY dcsListChanged)
    Q_PROPERTY(QString currentDcs READ currentDcs WRITE setCurrentDcs NOTIFY currentDcsChanged)
    Q_PROPERTY(bool isDataConnected READ isDataConnected NOTIFY dataConnectionChanged)
    Q_PROPERTY(QString connectionStatus READ connectionStatus NOTIFY dataConnectionChanged)

public:
    explicit DataScreen(QObject *parent = nullptr);

    QString message() const;
    void setMessage(const QString &message);

    // 大屏数据获取方法
    QString payload() const;
    QString mainSteamPressure() const;
    QString mainSteamTemp() const;
    QString reheatSteamPressure() const;
    QString reheatSteamTemp() const;
    QString mainSteamFlow() const;
    QString totalFuel() const;
    QString totalAir() const;
    QString oxygenContent() const;
    QString furnacePressure() const;

    // 烟气分析数据获取方法
    QString coContent() const;
    QString noxContent() const;
    QString so2Content() const;

    // DCS风机数据获取方法
    QString primaryFanA() const;
    QString primaryFanB() const;
    QString fanA() const;
    QString fanB() const;
    QString inducedFanA() const;
    QString inducedFanB() const;

    // 注释：数据大屏只需要实时显示，不需要数据列表获取方法
    bool isRunning() const;

    // 硬件数据相关方法
    QStringList boilerList() const;
    QString currentBoiler() const;
    QStringList dcsList() const;
    QString currentDcs() const;
    bool isDataConnected() const;
    QString connectionStatus() const;

    // 获取当前设备的采集间隔（秒）
    int getCurrentCollectionInterval() const;

public slots:
    void setIsRunning(bool running);
    void setCurrentBoiler(const QString &boiler);
    void setCurrentDcs(const QString &dcs);
    void startMonitoring();
    void stopMonitoring();

signals:
    void messageChanged();
    void dataChanged();
    void isRunningChanged();
    void boilerListChanged();
    void currentBoilerChanged();
    void dcsListChanged();
    void currentDcsChanged();
    void dataConnectionChanged();
    // 注释：移除smokeDataChanged信号，数据大屏不需要数据列表变化通知

private slots:
    void updateData();

private:
    void loadBoilerList();
    void loadDcsList();
    void updateTimerInterval();
    void updateSmokeData(); // 数据更新方法（仅更新实时显示数据）
    void updateDcsData(); // DCS数据更新方法

private:
    QString m_message;

    // 大屏数据
    QString m_payload;
    QString m_mainSteamPressure;
    QString m_mainSteamTemp;
    QString m_reheatSteamPressure;
    QString m_reheatSteamTemp;
    QString m_mainSteamFlow;
    QString m_totalFuel;
    QString m_totalAir;
    QString m_oxygenContent;
    QString m_furnacePressure;

    // 烟气分析数据成员变量
    QString m_coContent;
    QString m_noxContent;
    QString m_so2Content;

    // DCS风机数据成员变量
    QString m_primaryFanA;
    QString m_primaryFanB;
    QString m_fanA;
    QString m_fanB;
    QString m_inducedFanA;
    QString m_inducedFanB;

    bool m_isRunning;
    QTimer *m_timer;

    // 硬件数据相关成员变量
    QStringList m_boilerList;
    QString m_currentBoiler;
    QStringList m_dcsList;
    QString m_currentDcs;
    bool m_isDataConnected;
    QString m_connectionStatus;

    // 注释：数据大屏不需要数据存储和时间管理变量
};

#endif // DATASCREEN_H
