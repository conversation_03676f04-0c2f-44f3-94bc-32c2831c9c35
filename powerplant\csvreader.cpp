#include "csvreader.h"
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QDebug>
#include <QStandardPaths>
#include <QCoreApplication>

CsvReader::CsvReader(QObject *parent)
    : QObject(parent)
{
}

QVariantList CsvReader::readDataByDate(const QString &boilerName, const QDate &date)
{
    QVariantList result;
    QString filePath = buildCsvFilePath(boilerName, date);
    
    if (!isValidCsvFile(filePath)) {
        qDebug() << "CSV file does not exist or cannot be read:" << filePath;
        return result;
    }
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "Cannot open CSV file:" << filePath;
        return result;
    }
    
    QTextStream in(&file);
    // Qt 6中默认使用UTF-8编码，无需手动设置
    
    // 跳过表头
    if (!in.atEnd()) {
        in.readLine();
    }
    
    // 读取数据行
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (!line.isEmpty()) {
            QVariantMap dataPoint = parseCsvLine(line);
            if (!dataPoint.isEmpty()) {
                result.append(dataPoint);
            }
        }
    }
    
    file.close();
    qDebug() << "Successfully read CSV data," << result.size() << "records, file:" << filePath;
    return result;
}

QVariantList CsvReader::readDataByDateRange(const QString &boilerName, const QDate &startDate, const QDate &endDate)
{
    QVariantList result;
    
    QDate currentDate = startDate;
    while (currentDate <= endDate) {
        QVariantList dayData = readDataByDate(boilerName, currentDate);
        result.append(dayData);
        currentDate = currentDate.addDays(1);
    }
    
    return result;
}

QStringList CsvReader::getAvailableDates(const QString &boilerName)
{
    QStringList dates;

    // 获取data目录路径
    QString dataDir = QCoreApplication::applicationDirPath() + "/data";
    QDir dir(dataDir);

    qDebug() << "Application dir path:" << QCoreApplication::applicationDirPath();
    qDebug() << "Current working directory:" << QDir::currentPath();
    qDebug() << "Looking for data in directory:" << dataDir;
    qDebug() << "Directory exists:" << dir.exists();
    qDebug() << "Searching for boiler:" << boilerName;

    if (!dir.exists()) {
        qDebug() << "Data directory does not exist:" << dataDir;
        // 尝试从当前工作目录查找
        QString currentDataDir = QDir::currentPath() + "/data";
        QDir currentDir(currentDataDir);
        qDebug() << "Trying current working directory:" << currentDataDir;
        qDebug() << "Current dir exists:" << currentDir.exists();

        if (currentDir.exists()) {
            dataDir = currentDataDir;
            dir = currentDir;
            qDebug() << "Using current working directory for data";
        } else {
            return dates;
        }
    }
    
    // 查找匹配的CSV文件
    QString pattern = QString("%1-*.csv").arg(boilerName);
    QStringList filters;
    filters << pattern;

    qDebug() << "Search pattern:" << pattern;

    QFileInfoList fileList = dir.entryInfoList(filters, QDir::Files, QDir::Name);
    qDebug() << "Found" << fileList.size() << "files matching pattern";
    
    for (const QFileInfo &fileInfo : fileList) {
        QString fileName = fileInfo.baseName();
        qDebug() << "Processing file:" << fileName;

        // 提取日期部分 (格式: boilerName-YYYYMMDD.csv 或 boilerName-YYYYMMDD-HHMMSS.csv)
        QString datePart = fileName.mid(boilerName.length() + 1); // +1 for the dash
        qDebug() << "Date part:" << datePart;

        if (datePart.length() >= 8) {
            QString dateStr = datePart.left(8); // YYYYMMDD
            qDebug() << "Date string:" << dateStr;
            QDate date = QDate::fromString(dateStr, "yyyyMMdd");
            if (date.isValid()) {
                QString dateString = date.toString("yyyy-MM-dd");
                qDebug() << "Valid date found:" << dateString;
                if (!dates.contains(dateString)) {
                    dates.append(dateString);
                }
            } else {
                qDebug() << "Invalid date format:" << dateStr;
            }
        }
    }
    
    dates.sort();
    qDebug() << "Final dates list:" << dates;
    return dates;
}

bool CsvReader::hasDataForDate(const QString &boilerName, const QDate &date)
{
    QString filePath = buildCsvFilePath(boilerName, date);
    return isValidCsvFile(filePath);
}

QString CsvReader::buildCsvFilePath(const QString &boilerName, const QDate &date)
{
    QString dataDir = QCoreApplication::applicationDirPath() + "/data";
    QDir dir(dataDir);

    // 如果应用程序目录下没有data文件夹，尝试当前工作目录
    if (!dir.exists()) {
        QString currentDataDir = QDir::currentPath() + "/data";
        QDir currentDir(currentDataDir);
        if (currentDir.exists()) {
            dataDir = currentDataDir;
        }
    }

    QString dateStr = date.toString("yyyyMMdd");
    QString fileName = QString("%1-%2.csv").arg(boilerName, dateStr);
    QString filePath = QDir(dataDir).absoluteFilePath(fileName);
    qDebug() << "Building CSV file path:" << filePath;
    return filePath;
}

QVariantMap CsvReader::parseCsvLine(const QString &line)
{
    QVariantMap result;

    QStringList fields = line.split(',');
    if (fields.size() < 8) {
        qDebug() << "CSV行格式不正确，字段数不足:" << line;
        return result;
    }

    // 扩展CSV格式: 时间戳,O2,CO,NOx,SO2,测点温度,电压,电流,炉膛压力,发电机功率,主蒸汽压力,总风量,水煤比,一次风机A,一次风机B,送风机A,送风机B,引风机A,引风机B
    bool ok;
    qint64 timestamp = fields[0].toLongLong(&ok);
    if (!ok) {
        qDebug() << "时间戳解析失败:" << fields[0];
        return result;
    }

    QDateTime dateTime = QDateTime::fromSecsSinceEpoch(timestamp);

    result["timestamp"] = timestamp;
    result["datetime"] = dateTime.toString("yyyy-MM-dd hh:mm:ss");
    result["time"] = dateTime.toString("hh:mm:ss");

    // 烟气分析仪数据
    result["o2"] = fields[1].toDouble();
    result["co"] = fields[2].toDouble();
    result["nox"] = fields[3].toDouble();
    result["so2"] = fields[4].toDouble();
    result["temperature"] = fields[5].toDouble();
    result["voltage"] = fields[6].toDouble();
    result["current"] = fields[7].toDouble();

    // DCS数据（如果存在）
    if (fields.size() >= 19) {
        result["furnace_pressure"] = fields[8].toDouble();
        result["generator_power"] = fields[9].toDouble();
        result["main_steam_pressure"] = fields[10].toDouble();
        result["total_air_flow"] = fields[11].toDouble();
        result["water_coal_ratio"] = fields[12].toDouble();
        result["primary_fan_a"] = fields[13].toDouble();
        result["primary_fan_b"] = fields[14].toDouble();
        result["fan_a"] = fields[15].toDouble();
        result["fan_b"] = fields[16].toDouble();
        result["induced_fan_a"] = fields[17].toDouble();
        result["induced_fan_b"] = fields[18].toDouble();
    } else {
        // 如果没有DCS数据，设置默认值
        result["furnace_pressure"] = 0.0;
        result["generator_power"] = 0.0;
        result["main_steam_pressure"] = 0.0;
        result["total_air_flow"] = 0.0;
        result["water_coal_ratio"] = 0.0;
        result["primary_fan_a"] = 0.0;
        result["primary_fan_b"] = 0.0;
        result["fan_a"] = 0.0;
        result["fan_b"] = 0.0;
        result["induced_fan_a"] = 0.0;
        result["induced_fan_b"] = 0.0;
    }

    return result;
}

bool CsvReader::isValidCsvFile(const QString &filePath)
{
    QFile file(filePath);
    return file.exists() && file.size() > 0;
}
