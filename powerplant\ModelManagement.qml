import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: modelManagement
    
    // 信号定义
    signal navigateBack()
    signal navigateToSelection()
    signal navigateToPassword(string targetModel)
    signal navigateToSecondPassword(string targetModel)
    signal navigateToConfirmation(string targetModel)
    signal navigateToLoading(string targetModel)
    
    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回首页"
                onClicked: modelManagement.navigateBack()
            }
            Label {
                text: "模型管理"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }
        }
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 40
        spacing: 30

        // 当前模型状态区域
        Rectangle {
            Layout.fillWidth: true
            height: 120
            color: "#f0f0f0"
            radius: 12
            border.color: "#d0d0d0"

            RowLayout {
                anchors.fill: parent
                anchors.margins: 25

                Rectangle {
                    width: 16
                    height: 16
                    radius: 8
                    color: modelManager.isLoading ? "#ff9800" : "#4caf50"
                }

                ColumnLayout {
                    Layout.fillWidth: true
                    spacing: 8

                    Label {
                        text: modelManager.isLoading ? "模型加载中..." : "当前使用的模型"
                        font.pixelSize: 18
                        color: "#000000"
                    }

                    Label {
                        text: modelManager.currentModel
                        font.pixelSize: 24
                        font.bold: true
                        color: "#000000"
                    }
                }
            }
        }

        Label {
            text: "可用模型列表:"
            font.pixelSize: 20
            font.bold: true
            color: "#ffffff"
        }

        ListView {
            id: modelListView
            Layout.fillWidth: true
            Layout.fillHeight: true
            model: modelManager.availableModels
            spacing: 10
            delegate: Rectangle {
                width: parent.width
                height: 100
                color: modelData === modelManager.currentModel ? "#e3f2fd" : "#ffffff"
                border.color: modelData === modelManager.currentModel ? "#2196f3" : "#e0e0e0"
                border.width: 1
                radius: 4

                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 20

                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8

                        Label {
                            text: modelData
                            font.pixelSize: 20
                            font.bold: modelData === modelManager.currentModel
                        }

                        Label {
                            text: modelManager.getModelDescription(modelData)
                            font.pixelSize: 16
                            color: "#666"
                            wrapMode: Text.WordWrap
                            Layout.fillWidth: true
                        }
                    }

                    Label {
                        text: modelData === modelManager.currentModel ? "使用中" : ""
                        font.pixelSize: 16
                        color: "#4caf50"
                        font.bold: true
                        visible: modelData === modelManager.currentModel
                    }
                }
            }
        }

        Button {
            text: "选择使用新模型"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 300
            Layout.preferredHeight: 60
            enabled: !modelManager.isLoading
            onClicked: modelManagement.navigateToSelection()

            background: Rectangle {
                color: parent.enabled ? (parent.pressed ? "#1976d2" : "#2196f3") : "#cccccc"
                radius: 8
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 20
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
    }
}
