import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: homePage
    
    // 信号定义
    signal navigateToModelList()
    signal navigateToMonitoring()
    signal navigateToDataScreen()
    signal navigateToVideo()
    signal navigateToParameterAdjustment()
    
    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }
    
    ColumnLayout {
        anchors.centerIn: parent
        spacing: 40
        
        Label {
            text: "华润电力湖北有限公司\n锅炉智慧燃烧系统"
            font.pixelSize: 48
            font.bold: true
            color: "#ffffff"
            Layout.alignment: Qt.AlignHCenter
            horizontalAlignment: Text.AlignHCenter
        }
        
Button {
            text: "多维智慧燃烧控制系统"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 300
            Layout.preferredHeight: 80
            onClicked: homePage.navigateToDataScreen()

            background: Rectangle {
                color: parent.pressed ? "#ff6f00" : "#ff9800"
                radius: 10
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 24
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }

        Button {
            text: "锅炉燃烧数据监控系统"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 300
            Layout.preferredHeight: 80
            onClicked: homePage.navigateToMonitoring()

            background: Rectangle {
                color: parent.pressed ? "#388e3c" : "#4caf50"
                radius: 10
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 24
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }

        Button {
            text: "锅炉火焰图像识别系统"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 300
            Layout.preferredHeight: 80
            onClicked: homePage.navigateToVideo()

            background: Rectangle {
                color: parent.pressed ? "#7b1fa2" : "#9c27b0"
                radius: 10
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 24
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }

        Button {
            text: "参数调整"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 300
            Layout.preferredHeight: 80
            onClicked: homePage.navigateToParameterAdjustment()

            background: Rectangle {
                color: parent.pressed ? "#e65100" : "#ff5722"
                radius: 10
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 24
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }

        Button {
            text: "模型列表"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 300
            Layout.preferredHeight: 80
            onClicked: homePage.navigateToModelList()

            background: Rectangle {
                color: parent.pressed ? "#1976d2" : "#2196f3"
                radius: 10
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 24
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
    }
}
