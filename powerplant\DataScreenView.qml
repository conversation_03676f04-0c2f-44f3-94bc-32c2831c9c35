import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: dataScreenPage
    
    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }
    
    // 添加属性来存储锅炉参数数据 - 全部设为0
    property var boilerData: {
        "coalHeatValue": "0.0",
        "oxygenContent": "0.0",
        "coContent": "0.0",
        "exhaustTemp": "0.0",
        "flyAshCarbon": "0.0",
        "noxContent": "0.0",
        "boilerEfficiency": "0.0",
        "ignitionTime": "0",
        "highestEfficiency": "0.0"
    }
    
    // 存储原始数值（不带单位）的属性 - 全部设为0
    property var originalBoilerValues: ({
        "coalHeatValue": 0.0,
        "oxygenContent": 0.0,
        "coContent": 0.0,
        "exhaustTemp": 0.0,
        "flyAshCarbon": 0.0,
        "noxContent": 0.0,
        "boilerEfficiency": 0.0
    })
    
    // 页面加载完成后启动硬件数据监控
    Component.onCompleted: {
        console.log("DataScreenView 组件已完成加载");
        console.log("初始数据值 - O2:", dataScreen.oxygenContent, "CO:", dataScreen.coContent, "NOx:", dataScreen.noxContent, "SO2:", dataScreen.so2Content);

        // 启动数据大屏的硬件数据监控
        dataScreen.setIsRunning(true);

        // 如果有可用的锅炉，设置第一个为默认选择
        if (dataScreen.boilerList.length > 0 && !dataScreen.currentBoiler) {
            dataScreen.setCurrentBoiler(dataScreen.boilerList[0]);
            console.log("设置默认锅炉:", dataScreen.boilerList[0]);
        }

        console.log("数据大屏硬件监控已启动，当前锅炉:", dataScreen.currentBoiler);

        // 基于配置的动态延迟获取数据
        var collectionInterval = dataScreen.getCurrentCollectionInterval();
        var firstDelay = collectionInterval * 1000;      // 1个采集周期
        var secondDelay = collectionInterval * 1500;     // 1.5个采集周期

        console.log("数据大屏: 使用动态延迟 - 采集间隔=" + collectionInterval + "秒, 第一次延迟=" + firstDelay + "毫秒");

        initialDataTimer.interval = firstDelay;
        secondaryDataTimer.interval = secondDelay;
        initialDataTimer.start();
    }

    // 动态初始数据获取定时器
    Timer {
        id: initialDataTimer
        repeat: false
        onTriggered: {
            var interval = dataScreen.getCurrentCollectionInterval();
            console.log("数据大屏: 延迟" + interval + "秒后获取初始数据");
            updateDisplayWithHardwareData();

            // 如果数据仍然为空，启动二次获取
            if (dataScreen.oxygenContent === "0.0" || dataScreen.coContent === "0.0") {
                console.log("数据大屏: 数据仍为空，启动二次延迟获取");
                secondaryDataTimer.start();
            }
        }
    }

    // 动态二次数据获取定时器
    Timer {
        id: secondaryDataTimer
        repeat: false
        onTriggered: {
            var interval = dataScreen.getCurrentCollectionInterval();
            console.log("数据大屏: 延迟" + (interval * 1.5) + "秒后二次获取数据");
            updateDisplayWithHardwareData();
        }
    }

    // 页面销毁时停止监控
    Component.onDestruction: {
        console.log("DataScreenView 组件销毁，停止硬件监控");
        dataScreen.setIsRunning(false);
    }

    // 监听硬件数据变化
    Connections {
        target: dataScreen
        function onDataChanged() {
            console.log("数据大屏接收到硬件数据更新");
            console.log("当前数据值 - O2:", dataScreen.oxygenContent, "CO:", dataScreen.coContent, "NOx:", dataScreen.noxContent, "SO2:", dataScreen.so2Content);
            updateDisplayWithHardwareData();
        }
    }

    // 监听连接状态变化
    Connections {
        target: dataScreen
        function onDataConnectionChanged() {
            console.log("数据大屏连接状态变化:", dataScreen.isDataConnected, dataScreen.connectionStatus);
            updateConnectionStatus();
        }
    }

    // 监听锅炉列表变化
    Connections {
        target: dataScreen
        function onBoilerListChanged() {
            console.log("锅炉列表已更新:", dataScreen.boilerList);
            // 如果当前没有选择锅炉且有可用锅炉，自动选择第一个
            if (!dataScreen.currentBoiler && dataScreen.boilerList.length > 0) {
                dataScreen.setCurrentBoiler(dataScreen.boilerList[0]);
                console.log("自动选择第一个锅炉:", dataScreen.boilerList[0]);
            }
        }
    }

    // 更新显示数据的函数
    function updateDisplayWithHardwareData() {
        console.log("数据大屏显示数据已更新 - O2:", dataScreen.oxygenContent, "CO:", dataScreen.coContent, "NOx:", dataScreen.noxContent, "SO2:", dataScreen.so2Content);

        // 更新右侧表格数据
        if (typeof rightSideBox !== 'undefined' && rightSideBox.updateTableData) {
            rightSideBox.updateTableData();
        }
    }

    // 更新连接状态显示
    function updateConnectionStatus() {
        // 这里可以添加连接状态的视觉反馈
        console.log("连接状态:", dataScreen.isDataConnected ? "已连接" : "未连接");
    }
    
    // 注释：移除了随机数生成函数，现在直接从硬件获取数据
    
    // 更新左侧盒子数值的函数
    function updateLeftBoxValue(textElement, dataKey, unit) {
        if (!textElement || !originalBoilerValues[dataKey]) {
            console.log("左侧盒子更新失败:", dataKey);
            return;
        }
        
        // 获取原始值
        var originalValue = originalBoilerValues[dataKey];
        
        // 计算随机变动范围（5%-10%）
        var minChange = originalValue * 0.05;
        var maxChange = originalValue * 0.10;
        var change = (Math.random() * (maxChange - minChange)) + minChange;
        
        // 随机决定增加或减少
        if (Math.random() > 0.5) {
            change = -change;
        }
        
        // 计算新值
        var newValue = originalValue + change;
        
        // 格式化新值（保留2位小数）
        textElement.text = newValue.toFixed(2);
        
        // 同时更新数据模型
        dataScreenPage.boilerData[dataKey] = newValue.toFixed(2);
        
        console.log("左侧盒子已更新:", dataKey, newValue.toFixed(2));
    }
    
    // 更新中心盒子数值的函数
    function updateCenterBoxValue(textElement, dataKey, unit) {
        if (!textElement || centerContainer.originalCenterValues[dataKey] === undefined) {
            console.log("中心盒子更新失败:", dataKey);
            return;
        }
        
        // 获取原始值
        var originalValue = centerContainer.originalCenterValues[dataKey];
        
        // 计算随机变动范围（5%-10%）
        var minChange = Math.abs(originalValue) * 0.05;
        var maxChange = Math.abs(originalValue) * 0.10;
        var change = (Math.random() * (maxChange - minChange)) + minChange;
        
        // 随机决定增加或减少
        if (Math.random() > 0.5) {
            change = -change;
        }
        
        // 计算新值
        var newValue = originalValue + change;
        
        // 特殊处理负值（如炉膛压力）
        if (originalValue < 0) {
            // 确保负值保持为负
            newValue = -Math.abs(newValue);
        }
        
        // 格式化新值（保留适当精度）
        var precision = 1; // 默认保留1位小数
        
        // 根据不同类型的数据设置不同的精度
        if (dataKey === "co2Content" || dataKey === "oxygenContent") {
            precision = 2;
        } else if (dataKey === "coContent" || dataKey === "so2Content" || dataKey === "furnacePressure") {
            precision = 0; // 整数
        } else if (dataKey === "noxContent") {
            precision = 0; // 整数
        }
        
        // 更新文本元素
        var displayValue = newValue.toFixed(precision);
        if (originalValue >= 0 && dataKey !== "furnacePressure") {
            displayValue = Math.abs(newValue).toFixed(precision);
        }
        
        // 添加单位并更新文本
        textElement.text = displayValue + " " + unit;
        
        // 同时更新数据模型
        centerContainer.centerData[dataKey] = displayValue + " " + unit;
        
        console.log("中心盒子已更新:", dataKey, displayValue, unit);
    }
    
    // 注释：移除了定时器，现在使用硬件数据更新
    
    // 全屏容器
    Item {
        anchors.fill: parent
        
        // 背景图片
        Image {
            id: backgroundImage
            anchors.fill: parent
            source: "qrc:/images/virtual-bg.png"
            fillMode: Image.PreserveAspectCrop
            cache: true
        }
        
        // 顶部标题背景
        Image {
            id: topTitleBackground
            width: parent.width
            height: 60
            source: "qrc:/images/top-title.png"
            fillMode: Image.Stretch
        }
        
        // 顶部标题文本 - 使用Canvas实现渐变
        Item {
            anchors.centerIn: topTitleBackground
            width: 400  // 根据实际需要调整宽度
            height: 40  // 根据实际需要调整高度
            
            Canvas {
                anchors.fill: parent
                onPaint: {
                    var ctx = getContext("2d");
                    var gradient = ctx.createLinearGradient(0, 0, 0, height);
                    gradient.addColorStop(0, "#FFFFFF");
                    gradient.addColorStop(1, "#32C4FF");
                    
                    ctx.font = "bold 20px sans-serif";
                    ctx.textAlign = "center";
                    ctx.textBaseline = "middle";
                    ctx.fillStyle = gradient;
                    ctx.fillText("华润电力湖北有限公司锅炉智慧燃烧系统", width/2, height/2);
                }
            }
        }

        // 动态锅炉按钮容器
        Row {
            id: topButtonsContainer
            anchors.left: parent.left
            anchors.leftMargin: 11 // 距离左侧11px
            anchors.top: parent.top
            anchors.topMargin: 39 // 距离顶部39px
            spacing: 10
            z: 10 // 确保在其他元素上方

            // 动态生成锅炉按钮
            Repeater {
                id: boilerButtonRepeater
                model: dataScreen.boilerList

                Item {
                    id: boilerButton
                    width: 79
                    height: 26

                    property bool isActive: dataScreen.currentBoiler === modelData

                    // 按钮背景
                    Image {
                        anchors.fill: parent
                        source: parent.isActive ? "qrc:/images/top-btn-active.png" : "qrc:/images/top-btn.png"
                        fillMode: Image.Stretch
                    }

                    // 按钮文本 - 显示配置文件中的真实锅炉名称
                    Text {
                        anchors.centerIn: parent
                        text: modelData // 显示配置文件中的锅炉名称，如"Boiler1"
                        color: "white"
                        font.pixelSize: 12
                        font.bold: parent.isActive
                    }

                    // 鼠标区域
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            // 切换到选中的锅炉
                            dataScreen.setCurrentBoiler(modelData);
                            console.log("切换到锅炉:", modelData);

                            // 更新标题名称中的文本（显示锅炉名称）
                            boilerNumberText.text = modelData;
                        }
                    }
                }
            }
        }

        // 连接状态指示器（已隐藏）
        Rectangle {
            id: connectionStatusIndicator
            width: 200
            height: 30
            anchors.right: parent.right
            anchors.rightMargin: 200 // 在返回按钮左侧
            anchors.top: parent.top
            anchors.topMargin: 40
            z: 10
            color: Qt.rgba(0, 0, 0, 0.6)
            radius: 5
            visible: false  // 隐藏连接状态指示器

            Row {
                anchors.centerIn: parent
                spacing: 8

                Rectangle {
                    width: 12
                    height: 12
                    radius: 6
                    color: dataScreen.isDataConnected ? "#4caf50" : "#f44336"
                }

                Text {
                    text: dataScreen.connectionStatus
                    color: "white"
                    font.pixelSize: 12
                    anchors.verticalCenter: parent.verticalCenter
                }
            }
        }

        // 添加返回首页按钮
        Item {
            id: backHomeButton
            width: 169 // 设置适当的宽度
            height: 53 // 设置适当的高度
            anchors.right: parent.right
            anchors.rightMargin: 20 // 距离右侧20px
            anchors.top: parent.top
            anchors.topMargin: 35 // 与锅炉按钮对齐
            z: 10 // 确保在其他元素上方

            Image {
                id: backHomeImage
                anchors.fill: parent
                source: "qrc:/images/backhome.png"
                fillMode: Image.PreserveAspectFit
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    // 返回首页
                    stackView.pop();
                }
            }
        }

        // 修改顶部标题名称图片，添加动态文本
        Item {
            id: titleNameContainer
            anchors.top: topTitleBackground.bottom
            width: parent.width
            height: 40
            z: 5 // 确保在按钮下方，但在其他元素上方

            // 背景图片
            Image {
                id: titleNameImage
                anchors.fill: parent
                source: "qrc:/images/top-title-name.png"
                fillMode: Image.PreserveAspectFit
                anchors.horizontalCenter: parent.horizontalCenter
            }

            // 锅炉编号文本 - 动态显示当前选中的锅炉名称
            Text {
                id: boilerNumberText
                anchors.centerIn: parent
                text: dataScreen.currentBoiler || "未选择锅炉" // 显示当前选中的锅炉名称
                color: "white"
                font.pixelSize: 20
                font.bold: true
            }
        }

        // 左侧标题盒子
        Rectangle {
            id: leftTitleBox
            x: 26
            y: titleNameContainer.y + titleNameContainer.height + 10 // 增加间距，确保不会盖住top-title-name.png
            width: 383
            height: 40
            color: Qt.rgba(15/255, 87/255, 181/255, 0.55) // rgba(15, 87, 181, 0.55)

            // 左侧色块
            Rectangle {
                id: leftColorBlock
                width: 5
                height: parent.height
                color: "#6ABFFF"
            }

            // 左侧标题文本
            Text {
                x: leftColorBlock.width + 10
                anchors.verticalCenter: parent.verticalCenter
                text: "煤制、飞灰在线检测"
                color: "white"
                font.pixelSize: 16
            }
        }

        // 左侧边框盒子
        Rectangle {
            id: leftSideBox
            x: 26
            y: leftTitleBox.y + leftTitleBox.height + 8 // 保持与标题盒子的间距
            width: 383
            height: 810
            color: Qt.rgba(2/255, 22/255, 49/255, 0.62)
            border.width: 1
            border.color: Qt.rgba(144/255, 209/255, 255/255, 0.38)

            // 左上角三角形
            Canvas {
                id: leftTopTriangle
                x: 0
                y: 0
                width: 10
                height: 10
                onPaint: {
                    var ctx = getContext("2d");
                    ctx.fillStyle = "#70C2FF";
                    ctx.beginPath();
                    ctx.moveTo(0, 0);
                    ctx.lineTo(10, 0);
                    ctx.lineTo(0, 10);
                    ctx.closePath();
                    ctx.fill();
                }
            }

            // 右上角三角形
            Canvas {
                id: rightTopTriangle
                x: parent.width - 10
                y: 0
                width: 10
                height: 10
                onPaint: {
                    var ctx = getContext("2d");
                    ctx.fillStyle = "#70C2FF";
                    ctx.beginPath();
                    ctx.moveTo(10, 0);
                    ctx.lineTo(10, 10);
                    ctx.lineTo(0, 0);
                    ctx.closePath();
                    ctx.fill();
                }
            }

            // 左下角三角形
            Canvas {
                id: leftBottomTriangle
                x: 0
                y: parent.height - 10
                width: 10
                height: 10
                onPaint: {
                    var ctx = getContext("2d");
                    ctx.fillStyle = "#70C2FF";
                    ctx.beginPath();
                    ctx.moveTo(0, 10);
                    ctx.lineTo(0, 0);
                    ctx.lineTo(10, 10);
                    ctx.closePath();
                    ctx.fill();
                }
            }

            // 右下角三角形
            Canvas {
                id: rightBottomTriangle
                x: parent.width - 10
                y: parent.height - 10
                width: 10
                height: 10
                onPaint: {
                    var ctx = getContext("2d");
                    ctx.fillStyle = "#70C2FF";
                    ctx.beginPath();
                    ctx.moveTo(10, 10);
                    ctx.lineTo(0, 10);
                    ctx.lineTo(10, 0);
                    ctx.closePath();
                    ctx.fill();
                }
            }

            // 添加七个小盒子
            Column {
                anchors.left: parent.left
                anchors.right: parent.right
                anchors.top: parent.top
                anchors.leftMargin: 18
                anchors.rightMargin: 17
                anchors.topMargin: 18
                spacing: 18

                // 小盒子1 - 固定碳
                Rectangle {
                    id: leftBox1
                    width: parent.width
                    height: 95
                    radius: 9
                    color: Qt.rgba(126/255, 227/255, 255/255, 0.1)

                    // 标题文本
                    Text {
                        id: leftBox1Title
                        x: 23
                        y: 13
                        text: "固定碳"
                        color: "#ffffff"
                        font.pixelSize: 18
                    }

                    // 数值文本
                    Text {
                        id: leftBox1Value
                        x: 23
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: dataScreenPage.boilerData.coalHeatValue
                        color: "#FF6C6C"
                        font.pixelSize: 28
                        font.bold: true
                    }

                    // 单位文本
                    Text {
                        id: leftBox1Unit
                        anchors.right: parent.right
                        anchors.rightMargin: 18
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: "%"
                        color: "white"
                        font.pixelSize: 14
                    }
                }

                // 小盒子2 - 全硫
                Rectangle {
                    id: leftBox2
                    width: parent.width
                    height: 95
                    radius: 9
                    color: Qt.rgba(126/255, 227/255, 255/255, 0.1)

                    // 标题文本
                    Text {
                        id: leftBox2Title
                        x: 23
                        y: 13
                        text: "全硫"
                        color: "#ffffff"
                        font.pixelSize: 18
                    }

                    // 数值文本
                    Text {
                        id: leftBox2Value
                        x: 23
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: dataScreenPage.boilerData.oxygenContent
                        color: "#75C1F0"
                        font.pixelSize: 28
                        font.bold: true
                    }

                    // 单位文本
                    Text {
                        id: leftBox2Unit
                        anchors.right: parent.right
                        anchors.rightMargin: 18
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: "%"
                        color: "white"
                        font.pixelSize: 14
                    }
                }

                // 小盒子3-7 (简化版本，只显示基本结构)
                Rectangle {
                    id: leftBox3
                    width: parent.width
                    height: 95
                    radius: 9
                    color: Qt.rgba(126/255, 227/255, 255/255, 0.1)

                    Text {
                        x: 23
                        y: 13
                        text: "灰分"
                        color: "#ffffff"
                        font.pixelSize: 18
                    }

                    Text {
                        id: leftBox3Value
                        x: 23
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: dataScreenPage.boilerData.coContent
                        color: "#69D9CA"
                        font.pixelSize: 28
                        font.bold: true
                    }

                    Text {
                        anchors.right: parent.right
                        anchors.rightMargin: 18
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: "%"
                        color: "white"
                        font.pixelSize: 14
                    }
                }

                Rectangle {
                    id: leftBox4
                    width: parent.width
                    height: 95
                    radius: 9
                    color: Qt.rgba(126/255, 227/255, 255/255, 0.1)

                    Text {
                        x: 23
                        y: 13
                        text: "挥发分"
                        color: "#ffffff"
                        font.pixelSize: 18
                    }

                    Text {
                        id: leftBox4Value
                        x: 23
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: dataScreenPage.boilerData.exhaustTemp
                        color: "#DFC541"
                        font.pixelSize: 28
                        font.bold: true
                    }

                    Text {
                        anchors.right: parent.right
                        anchors.rightMargin: 18
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: "%"
                        color: "white"
                        font.pixelSize: 14
                    }
                }

                Rectangle {
                    id: leftBox5
                    width: parent.width
                    height: 95
                    radius: 9
                    color: Qt.rgba(126/255, 227/255, 255/255, 0.1)

                    Text {
                        x: 23
                        y: 13
                        text: "水分"
                        color: "#ffffff"
                        font.pixelSize: 18
                    }

                    Text {
                        id: leftBox5Value
                        x: 23
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: dataScreenPage.boilerData.flyAshCarbon
                        color: "#6D8FFF"
                        font.pixelSize: 28
                        font.bold: true
                    }

                    Text {
                        anchors.right: parent.right
                        anchors.rightMargin: 18
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: "%"
                        color: "white"
                        font.pixelSize: 14
                    }
                }

                Rectangle {
                    id: leftBox6
                    width: parent.width
                    height: 95
                    radius: 9
                    color: Qt.rgba(126/255, 227/255, 255/255, 0.1)

                    Text {
                        x: 23
                        y: 13
                        text: "低位热值"
                        color: "#ffffff"
                        font.pixelSize: 18
                    }

                    Text {
                        id: leftBox6Value
                        x: 23
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: dataScreenPage.boilerData.noxContent
                        color: "#6EE26C"
                        font.pixelSize: 28
                        font.bold: true
                    }

                    Text {
                        anchors.right: parent.right
                        anchors.rightMargin: 18
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: "Mj/kg"
                        color: "white"
                        font.pixelSize: 14
                    }
                }

                Rectangle {
                    id: leftBox7
                    width: parent.width
                    height: 95
                    radius: 9
                    color: Qt.rgba(126/255, 227/255, 255/255, 0.1)

                    Text {
                        x: 23
                        y: 13
                        text: "飞灰含碳量"
                        color: "#ffffff"
                        font.pixelSize: 18
                    }

                    Text {
                        id: leftBox7Value
                        x: 23
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: dataScreenPage.boilerData.boilerEfficiency
                        color: "#E36AE5"
                        font.pixelSize: 28
                        font.bold: true
                    }

                    Text {
                        anchors.right: parent.right
                        anchors.rightMargin: 18
                        anchors.bottom: parent.bottom
                        anchors.bottomMargin: 13
                        text: "%"
                        color: "white"
                        font.pixelSize: 14
                    }
                }
            }
        }

        // 中间部分容器 - 放在center-tool-fire.png上方
        Item {
            id: centerContainer
            x: (parent.width - width) / 2 - 50 // 居中后向左偏移50px
            y: titleNameContainer.y + titleNameContainer.height + 10 // 增加间距，确保不会盖住top-title-name.png
            width: parent.width - (26 + 383) - (15 + 450) - 20 // 左侧(26+383) + 右侧(15+450) + 额外间距20px
            height: firstRowBoxes.height + secondRowBoxes.height + 8 // 设置高度为两行盒子高度加上8px间距
            z: 10 // 确保在背景之上

            // 中间数据 - 全部设为0
            property var centerData: {
                "payload": "0 MW",
                "totalFuel": "0.0 t/h",
                "totalAir": "0 m³/h",
                "oxygenContent": "0.0 %",
                "furnacePressure": "0 Pa",
                "coContent": "0 ppm",
                "co2Content": "0.0 %",
                "so2Content": "0 ppm",
                "noxContent": "0 mg/Nm³",
                "temperature": "0 ℃"
            }
            
            // 存储原始数值（不带单位）的属性 - 全部设为0
            property var originalCenterValues: ({
                "payload": 0,
                "totalFuel": 0.0,
                "totalAir": 0,
                "oxygenContent": 0.0,
                "furnacePressure": 0,
                "coContent": 0,
                "co2Content": 0.0,
                "so2Content": 0,
                "noxContent": 0,
                "temperature": 0
            })

            // 注释：移除了中心数据定时器，现在使用硬件数据更新

            // 第一行盒子容器 - 使用Item而不是Row，以便手动定位子元素
            Item {
                id: firstRowBoxes
                anchors.top: parent.top
                anchors.left: parent.left
                anchors.right: parent.right
                anchors.leftMargin: 13 // 设置左侧距离为13px
                anchors.rightMargin: 13 // 设置右侧距离为13px
                height: 90

                // 计算每个盒子的宽度和间距
                property int totalWidth: width // 可用总宽度
                property int boxSpacing: 5 // 固定间距为10px
                property int boxWidth: (totalWidth - (boxSpacing * 4)) / 5 // 动态计算盒子宽度

                // 第一个盒子 - 发电机功率
                Rectangle {
                    id: box1
                    width: firstRowBoxes.boxWidth
                    height: parent.height
                    x: 0 // 从左侧开始
                    color: "transparent"

                    Item {
                        anchors.fill: parent
                        anchors.margins: 5

                        Image {
                            id: box1Image
                            width: sourceSize.width
                            height: sourceSize.height
                            source: "qrc:/images/center-payload.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Column {
                            anchors.left: box1Image.right
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 6
                            width: parent.width - box1Image.width - 16 // 确保文本不会超出盒子

                            Text {
                                id: box1Value
                                text: dataScreen.payload
                                color: "#97E6FF"
                                font.pixelSize: 20
                                font.bold: false
                            }

                            Text {
                                id: box1Name
                                text: "发电机功率"
                                color: "white"
                                font.pixelSize: 18
                            }
                        }
                    }
                }

                // 第二个盒子 - 总燃料量
                Rectangle {
                    id: box2
                    width: firstRowBoxes.boxWidth
                    height: parent.height
                    x: box1.x + box1.width + firstRowBoxes.boxSpacing
                    color: "transparent"

                    Item {
                        anchors.fill: parent
                        anchors.margins: 5

                        Image {
                            id: box2Image
                            width: sourceSize.width
                            height: sourceSize.height
                            source: "qrc:/images/center-fuel-quantity.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Column {
                            anchors.left: box2Image.right
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 6
                            width: parent.width - box2Image.width - 16 // 确保文本不会超出盒子

                            Text {
                                id: box2Value
                                text: dataScreen.totalFuel
                                color: "#97E6FF"
                                font.pixelSize: 20
                                font.bold: false
                            }

                            Text {
                                id: box2Name
                                text: "总燃料量"
                                color: "white"
                                font.pixelSize: 18
                            }
                        }
                    }
                }

                // 第三个盒子 - 总风量
                Rectangle {
                    id: box3
                    width: firstRowBoxes.boxWidth
                    height: parent.height
                    x: box2.x + box2.width + firstRowBoxes.boxSpacing
                    color: "transparent"

                    Item {
                        anchors.fill: parent
                        anchors.margins: 5

                        Image {
                            id: box3Image
                            width: sourceSize.width
                            height: sourceSize.height
                            source: "qrc:/images/center-air-quantity.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Column {
                            anchors.left: box3Image.right
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 6
                            width: parent.width - box3Image.width - 16 // 确保文本不会超出盒子

                            Text {
                                id: box3Value
                                text: dataScreen.totalAir
                                color: "#97E6FF"
                                font.pixelSize: 20
                                font.bold: false
                            }

                            Text {
                                id: box3Name
                                text: "总风量"
                                color: "white"
                                font.pixelSize: 18
                            }
                        }
                    }
                }

                // 第四个盒子 - 炉膛压力
                Rectangle {
                    id: box10
                    width: firstRowBoxes.boxWidth
                    height: parent.height
                    x: box3.x + box3.width + firstRowBoxes.boxSpacing
                    color: "transparent"

                    Item {
                        anchors.fill: parent
                        anchors.margins: 5

                        Image {
                            id: box10Image
                            width: sourceSize.width
                            height: sourceSize.height
                            source: "qrc:/images/center-main-pressure.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Column {
                            anchors.left: box10Image.right
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 6
                            width: parent.width - box10Image.width - 16 // 确保文本不会超出盒子

                            Text {
                                id: box10Value
                                text: dataScreen.furnacePressure
                                color: "#97E6FF"
                                font.pixelSize: 20
                                font.bold: false
                            }

                            Text {
                                id: box10Name
                                text: "炉膛压力"
                                color: "white"
                                font.pixelSize: 18
                            }
                        }
                    }
                }

                // 第五个盒子 - 过热器温度
                Rectangle {
                    id: box5
                    width: firstRowBoxes.boxWidth
                    height: parent.height
                    x: box10.x + box10.width + firstRowBoxes.boxSpacing
                    color: "transparent"

                    Item {
                        anchors.fill: parent
                        anchors.margins: 5

                        Image {
                            id: box5Image
                            width: sourceSize.width
                            height: sourceSize.height
                            source: "qrc:/images/center-temperature.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Column {
                            anchors.left: box5Image.right
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 6
                            width: parent.width - box5Image.width - 16 // 确保文本不会超出盒子

                            Text {
                                id: box5Value
                                text: dataScreen.mainSteamTemp
                                color: "#97E6FF"
                                font.pixelSize: 20
                                font.bold: false
                            }

                            Text {
                                id: box5Name
                                text: "过热器温度"
                                color: "white"
                                font.pixelSize: 18
                            }
                        }
                    }
                }
            }

            // 第二行盒子容器 - 使用Item而不是Row，以便手动定位子元素
            Item {
                id: secondRowBoxes
                anchors.top: firstRowBoxes.bottom
                anchors.topMargin: 8 // 保持上下行间距为8px
                anchors.left: parent.left
                anchors.right: parent.right
                anchors.leftMargin: 13 // 设置左侧距离为13px
                anchors.rightMargin: 13 // 设置右侧距离为13px
                height: 90

                // 计算每个盒子的宽度和间距
                property int totalWidth: width // 可用总宽度
                property int boxSpacing: 10 // 固定间距为10px
                property int boxWidth: (totalWidth - (boxSpacing * 4)) / 5 // 动态计算盒子宽度

                // 第一个盒子 - 氧量
                Rectangle {
                    id: box4
                    width: secondRowBoxes.boxWidth
                    height: parent.height
                    x: 0 // 从左侧开始
                    color: "transparent"

                    Item {
                        anchors.fill: parent
                        anchors.margins: 5

                        Image {
                            id: box4Image
                            width: sourceSize.width
                            height: sourceSize.height
                            source: "qrc:/images/center-oxygen-content.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Column {
                            anchors.left: box4Image.right
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 6
                            width: parent.width - box4Image.width - 16 // 确保文本不会超出盒子

                            Text {
                                id: box4Value
                                text: dataScreen.oxygenContent
                                color: "#97E6FF"
                                font.pixelSize: 20
                                font.bold: false
                            }

                            Text {
                                id: box4Name
                                text: "氧量"
                                color: "white"
                                font.pixelSize: 18
                            }
                        }
                    }
                }

                // 第二个盒子 - CO含量
                Rectangle {
                    id: box6
                    width: secondRowBoxes.boxWidth
                    height: parent.height
                    x: box4.x + box4.width + secondRowBoxes.boxSpacing
                    color: "transparent"

                    Item {
                        anchors.fill: parent
                        anchors.margins: 5

                        Image {
                            id: box6Image
                            width: sourceSize.width
                            height: sourceSize.height
                            source: "qrc:/images/center-CO.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Column {
                            anchors.left: box6Image.right
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 6
                            width: parent.width - box6Image.width - 16 // 确保文本不会超出盒子

                            Text {
                                id: box6Value
                                text: dataScreen.coContent
                                color: "#97E6FF"
                                font.pixelSize: 20
                                font.bold: false
                            }

                            Text {
                                id: box6Name
                                text: "CO含量"
                                color: "white"
                                font.pixelSize: 18
                            }
                        }
                    }
                }

                // 第三个盒子 - CO₂含量
                Rectangle {
                    id: box7
                    width: secondRowBoxes.boxWidth
                    height: parent.height
                    x: box6.x + box6.width + secondRowBoxes.boxSpacing
                    color: "transparent"

                    Item {
                        anchors.fill: parent
                        anchors.margins: 5

                        Image {
                            id: box7Image
                            width: sourceSize.width
                            height: sourceSize.height
                            source: "qrc:/images/center-CO2.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Column {
                            anchors.left: box7Image.right
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 6
                            width: parent.width - box7Image.width - 16 // 确保文本不会超出盒子

                            Text {
                                id: box7Value
                                text: centerContainer.centerData.co2Content
                                color: "#97E6FF"
                                font.pixelSize: 20
                                font.bold: false
                            }

                            Text {
                                id: box7Name
                                text: "CO₂含量"
                                color: "white"
                                font.pixelSize: 18
                            }
                        }
                    }
                }

                // 第四个盒子 - SO₂含量
                Rectangle {
                    id: box8
                    width: secondRowBoxes.boxWidth
                    height: parent.height
                    x: box7.x + box7.width + secondRowBoxes.boxSpacing
                    color: "transparent"

                    Item {
                        anchors.fill: parent
                        anchors.margins: 5

                        Image {
                            id: box8Image
                            width: sourceSize.width
                            height: sourceSize.height
                            source: "qrc:/images/center-SO2.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Column {
                            anchors.left: box8Image.right
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 6
                            width: parent.width - box8Image.width - 16 // 确保文本不会超出盒子

                            Text {
                                id: box8Value
                                text: dataScreen.so2Content
                                color: "#97E6FF"
                                font.pixelSize: 20
                                font.bold: false
                            }

                            Text {
                                id: box8Name
                                text: "SO₂含量"
                                color: "white"
                                font.pixelSize: 18
                            }
                        }
                    }
                }

                // 第五个盒子 - NOₓ含量
                Rectangle {
                    id: box9
                    width: secondRowBoxes.boxWidth
                    height: parent.height
                    x: box8.x + box8.width + secondRowBoxes.boxSpacing
                    color: "transparent"

                    Item {
                        anchors.fill: parent
                        anchors.margins: 5

                        Image {
                            id: box9Image
                            width: sourceSize.width
                            height: sourceSize.height
                            source: "qrc:/images/center-NOx.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Column {
                            anchors.left: box9Image.right
                            anchors.leftMargin: 5
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 6
                            width: parent.width - box9Image.width - 16 // 确保文本不会超出盒子

                            Text {
                                id: box9Value
                                text: dataScreen.noxContent
                                color: "#97E6FF"
                                font.pixelSize: 20
                                font.bold: false
                            }

                            Text {
                                id: box9Name
                                text: "NOₓ含量"
                                color: "white"
                                font.pixelSize: 18
                            }
                        }
                    }
                }
            }
        }

        // 中央锅炉图像
        Image {
            id: centerFireImage
            x: (parent.width - width) / 2 - 50 // 居中后向左偏移50px，与centerContainer保持一致
            y: centerContainer.y + centerContainer.height // 向下移动30px
            width: 1000  // 从600增加到700
            height: 700  // 从400增加到500
            source: "qrc:/images/center-tool-fire.png"
            fillMode: Image.PreserveAspectFit
            cache: true
        }

        // 火焰动画图片，放置在锅炉燃烧室内部
        AnimatedImage {
            id: centerFireAnimation
            // 基于锅炉图像的位置计算火焰位置，放置在燃烧室中央
            x: centerFireImage.x + centerFireImage.width * 0.08  // 锅炉图像左侧6%位置（往左移动）
            y: centerFireImage.y + centerFireImage.height * 0.52  // 锅炉图像顶部48%位置（往上移动）
            width: 125  // 调整火焰大小，使其更适合燃烧室
            height: sourceSize.height * (100 / sourceSize.width)  // 按比例计算高度
            source: "qrc:/images/center-fire.gif"
            playing: true
            z: 1  // 确保在锅炉图像上方
        }

        // 右侧标题盒子
        Rectangle {
            id: rightTitleBox
            x: parent.width - 15 - 450
            y: titleNameContainer.y + titleNameContainer.height + 10
            width: 450
            height: 40
            color: Qt.rgba(15/255, 87/255, 181/255, 0.55)

            Rectangle {
                id: rightColorBlock
                width: 5
                height: parent.height
                color: "#6ABFFF"
            }

            Text {
                x: rightColorBlock.width + 10
                anchors.verticalCenter: parent.verticalCenter
                text: "锅炉燃烧参数调整"
                color: "white"
                font.pixelSize: 16
            }
        }

        // 右侧边框盒子
        Rectangle {
            id: rightSideBox
            x: parent.width - 15 - 450
            y: rightTitleBox.y + rightTitleBox.height + 8
            width: 450
            height: 810
            color: Qt.rgba(2/255, 22/255, 49/255, 0.62)
            border.width: 1
            border.color: Qt.rgba(144/255, 209/255, 255/255, 0.38)

            // 表格容器
            Rectangle {
                id: tableContainer
                anchors.fill: parent
                anchors.margins: 13 // 表格距离盒子边距为13px
                color: "transparent" // 表格主题为透明色

                // 表头
                Rectangle {
                    id: tableHeader
                    width: parent.width
                    height: 30 // 表头高度30px
                    color: Qt.rgba(33/255, 150/255, 243/255, 0.4) // 表头颜色

                    // 表头行
                    Row {
                        anchors.fill: parent
                        
                        // 因素列表头 - 加宽以显示长文本
                        Rectangle {
                            width: parent.width * 0.5  // 50%宽度
                            height: parent.height
                            color: "transparent"

                            Text {
                                anchors.centerIn: parent
                                text: "因素"
                                color: "white"
                                font.pixelSize: 16
                                font.bold: true
                            }
                        }

                        // 运行参数列表头
                        Rectangle {
                            width: parent.width * 0.25  // 25%宽度
                            height: parent.height
                            color: "transparent"

                            Text {
                                anchors.centerIn: parent
                                text: "运行参数"
                                color: "white"
                                font.pixelSize: 16
                                font.bold: true
                            }
                        }

                        // 调整值列表头
                        Rectangle {
                            width: parent.width * 0.25  // 25%宽度
                            height: parent.height
                            color: "transparent"

                            Text {
                                anchors.centerIn: parent
                                text: "调整值"
                                color: "white"
                                font.pixelSize: 16
                                font.bold: true
                            }
                        }
                    }
                }
                
                // 表格内容 - 使用ListView
                ListView {
                    id: tableListView
                    anchors.top: tableHeader.bottom
                    width: parent.width
                    height: parent.height - tableHeader.height
                    interactive: true // 允许滚动
                    clip: true
                    model: tableModel
                    
                    // 定义每行的委托
                    delegate: Item {
                        width: tableListView.width
                        height: 85 // 每行高85px
                        
                        // 行内容
                        Row {
                            anchors.fill: parent
                            
                            // 因素列 - 加宽以显示长文本
                            Rectangle {
                                width: parent.width * 0.5  // 50%宽度
                                height: parent.height
                                color: "transparent"

                                Text {
                                    anchors.centerIn: parent
                                    text: factor
                                    color: "white"
                                    font.pixelSize: 15  // 稍微减小字体以适应更多文本
                                    wrapMode: Text.WordWrap
                                    horizontalAlignment: Text.AlignHCenter
                                    width: parent.width - 10  // 留出边距
                                }
                            }

                            // 运行参数列
                            Rectangle {
                                width: parent.width * 0.25  // 25%宽度
                                height: parent.height
                                color: "transparent"

                                Text {
                                    anchors.centerIn: parent
                                    text: currentValue
                                    color: "white"
                                    font.pixelSize: 15
                                    font.bold: true
                                }
                            }

                            // 调整值列
                            Rectangle {
                                width: parent.width * 0.25  // 25%宽度
                                height: parent.height
                                color: "transparent"

                                Text {
                                    anchors.centerIn: parent
                                    text: suggestedValue
                                    color: "white"
                                    font.pixelSize: 15
                                    font.bold: true
                                }
                            }
                        }
                        
                        // 分隔线
                        Rectangle {
                            width: parent.width
                            height: 1
                            color: "#1F3053" // 分隔线颜色
                            anchors.bottom: parent.bottom
                        }
                    }
                }
            }
            
            // 表格数据模型 - 初始数据设为0
            ListModel {
                id: tableModel

                // 初始数据设为0
                ListElement {
                    factor: "O₂含量"
                    currentValue: "0.0%"
                    suggestedValue: "0.0%"
                }

                ListElement {
                    factor: "CO含量"
                    currentValue: "0ppm"
                    suggestedValue: "0ppm"
                }

                ListElement {
                    factor: "一次风机A入口导叶位置"
                    currentValue: "0.0000%"
                    suggestedValue: "0.0000%"
                }

                ListElement {
                    factor: "一次风机B入口导叶位置"
                    currentValue: "0.0000%"
                    suggestedValue: "0.0000%"
                }

                ListElement {
                    factor: "送风机A入口导叶位置"
                    currentValue: "0.0000%"
                    suggestedValue: "0.0000%"
                }

                ListElement {
                    factor: "送风机B入口导叶位置"
                    currentValue: "0.0000%"
                    suggestedValue: "0.0000%"
                }

                ListElement {
                    factor: "引风机A入口导叶位置"
                    currentValue: "0.0000%"
                    suggestedValue: "0.0000%"
                }

                ListElement {
                    factor: "引风机B入口导叶位置"
                    currentValue: "0.0000%"
                    suggestedValue: "0.0000%"
                }
            }
            
            // 更新表格数据的函数
            function updateTableData() {
                // 更新O₂含量和CO含量的当前值，直接从dataScreen获取
                tableModel.setProperty(0, "currentValue", dataScreen.oxygenContent);
                tableModel.setProperty(1, "currentValue", dataScreen.coContent);

                // 更新风机数据，直接从dataScreen获取
                tableModel.setProperty(2, "currentValue", dataScreen.primaryFanA);
                tableModel.setProperty(3, "currentValue", dataScreen.primaryFanB);
                tableModel.setProperty(4, "currentValue", dataScreen.fanA);
                tableModel.setProperty(5, "currentValue", dataScreen.fanB);
                tableModel.setProperty(6, "currentValue", dataScreen.inducedFanA);
                tableModel.setProperty(7, "currentValue", dataScreen.inducedFanB);
            }
            
            // 连接硬件数据更新信号
            Connections {
                target: dataScreen
                function onDataChanged() {
                    // 当硬件数据更新时，同步更新表格数据
                    rightSideBox.updateTableData();
                }
            }
            
            // 初始化时更新一次表格数据
            Component.onCompleted: {
                updateTableData();
            }
        }
    }
}
