# DCS 通信协议说明文档

## 概述

本文档详细说明了锅炉智慧燃烧系统与 DCS（分布式控制系统）的通信协议，包括数据采集请求的发送和响应数据的解析过程。

## 通信模式

**主从模式（Master-Slave）**
- **主站**：锅炉智慧燃烧系统（本程序）
- **从站**：DCS 系统
- **协议**：Modbus RTU over RS485
- **通信方式**：主动请求-响应模式

## 配置参数

### 基本配置
```ini
[DCS1]
Desc = 分布式控制系统1
Protocol = DCS_RS485
CollectionInterval = 10          ; 采集间隔（秒）
DeviceAddress = 12              ; 设备地址（0x0C）
StartRegister = 31              ; 起始寄存器地址
RegisterCount = 44              ; 读取寄存器数量
AssociatedBoiler = #3机组       ; 关联的锅炉
```

### 串口配置
```ini
[DCS_RS485]
Port = COM6
BaudRate = 9600
StopBits = 1
Parity = N
DataBits = 8
Timeout = 1.0
```

## 数据采集请求

### 请求报文格式
```
字节位置: 0    1    2    3    4    5    6    7
内容:    设备  功能  起始地址    寄存器数量   CRC校验
        地址  码   高字节 低字节 高字节 低字节 低字节 高字节
示例:    0C   03   00    1F    00    2C    XX    XX
```

### 请求报文详解
- **设备地址（0x0C）**：对应配置中的 `DeviceAddress = 12`
- **功能码（0x03）**：读取保持寄存器
- **起始地址（0x001F）**：对应配置中的 `StartRegister = 31`
- **寄存器数量（0x002C）**：对应配置中的 `RegisterCount = 44`
- **CRC校验**：使用标准 Modbus CRC16 算法

### 代码实现
```cpp
// 构建请求报文
unsigned char request[8];
request[0] = device_address;                    // 0x0C
request[1] = 0x03;                             // 功能码
request[2] = (start_register >> 8) & 0xFF;     // 起始地址高字节
request[3] = start_register & 0xFF;            // 起始地址低字节
request[4] = (register_count >> 8) & 0xFF;     // 寄存器数量高字节
request[5] = register_count & 0xFF;            // 寄存器数量低字节

// 计算并添加CRC
unsigned short crc = calculate_crc(request, 6);
request[6] = crc & 0xFF;                       // CRC低字节
request[7] = (crc >> 8) & 0xFF;               // CRC高字节
```

## DCS 响应数据

### 响应报文格式
```
总长度：97 字节
结构：[7字节头部] + [88字节数据] + [2字节CRC]
```

### 响应头部格式
```
字节位置: 0    1    2    3    4    5    6
内容:    设备  功能  起始地址    寄存器数量   数据
        地址  码   高字节 低字节 高字节 低字节 长度
示例:    0C   10   00    1F    00    2C    58
```

### 数据部分（88字节）
数据部分包含 22 个 IEEE 754 格式的浮点数（每个 4 字节），按以下顺序排列：

## 数据映射配置

### 偏移量说明
所有偏移量都是相对于 **88字节数据部分** 的起始位置计算的。

```ini
; 基础工艺参数
FurnacePressureOffset = 0        ; 炉膛压力（字节0-3）
SuperheaterTempOffset = 4        ; 过热器平均温度（字节4-7）
GeneratorPowerOffset = 8         ; 发电机功率（字节8-11）
MainSteamPressureOffset = 12     ; 主蒸汽压力（字节12-15）
TotalAirFlowOffset = 16          ; 总风量（字节16-19）
WaterCoalRatioOffset = 24        ; 水煤比（字节24-27）

; 风机导叶位置参数
PrimaryFanAOffset = 28           ; 一次分机A入口导叶位置（字节28-31）
PrimaryFanBOffset = 32           ; 一次分机B入口导叶位置（字节32-35）
FanAOffset = 36                  ; 送风机A入口导叶位置（字节36-39）
FanBOffset = 40                  ; 送风机B入口导叶位置（字节40-43）
InducedFanAOffset = 44           ; 引风机A入口导叶位置（字节44-47）
InducedFanBOffset = 48           ; 引风机B入口导叶位置（字节48-51）

; 烟气成分参数
COOffset = 52                    ; CO浓度（字节52-55）
O2Offset = 56                    ; O2浓度（字节56-59）
SO2Offset = 60                   ; SO2浓度（字节60-63）
NOxOffset = 64                   ; NOx浓度（字节64-67）
```

## IEEE 754 浮点数解析

### 数据格式
- **格式**：IEEE 754 单精度浮点数
- **字节序**：大端序（Big-Endian）
- **长度**：4 字节

### 解析示例
```
原始字节: 40 FD 69 66
解析过程:
1. 大端序转小端序: 66 69 FD 40
2. IEEE 754 解析: 7.9134
```

### 代码实现
```cpp
float DCSDevice::parse_float(const unsigned char* buffer, int offset) {
    float value;
    unsigned char bytes[4];
    
    // IEEE 754大端序格式转换为小端序
    bytes[3] = buffer[offset];     // 最高字节
    bytes[2] = buffer[offset + 1];
    bytes[1] = buffer[offset + 2];
    bytes[0] = buffer[offset + 3]; // 最低字节
    
    // 转换为浮点数
    memcpy(&value, bytes, 4);
    return value;
}
```

## 数据采集流程

### 1. 发送请求
```cpp
// 发送 Modbus 读取请求
WriteFile(hSerial, request, 8, &bytesWritten, NULL);
```

### 2. 等待响应
```cpp
// 等待 DCS 处理时间
std::this_thread::sleep_for(std::chrono::milliseconds(200));
```

### 3. 读取响应
```cpp
// 读取响应数据
ReadFile(hSerial, buffer, sizeof(buffer), &bytesRead, NULL);
```

### 4. 验证报文头
```cpp
// 查找有效的 DCS 响应报文头
for (int i = 0; i <= read_len - 97; i++) {
    if (buffer[i] == 0x0C && buffer[i+1] == 0x10 &&
        buffer[i+2] == 0x00 && buffer[i+3] == 0x1F &&
        buffer[i+4] == 0x00 && buffer[i+5] == 0x2C &&
        buffer[i+6] == 0x58) {
        found_dcs_frame = true;
        frame_start = i;
        break;
    }
}
```

### 5. 解析数据
```cpp
// 跳过7字节头部，解析88字节数据
unsigned char* data_start = &buffer[frame_start + 7];

// 根据偏移量解析各参数
float furnace_pressure = parse_float(data_start, furnace_pressure_offset);
float superheater_temp = parse_float(data_start, superheater_temp_offset);
// ... 其他参数
```

## 数据参数说明

| 参数名称 | 单位 | 偏移量 | 说明 |
|---------|------|--------|------|
| 炉膛压力 | Pa | 0 | 锅炉炉膛内的压力值 |
| 过热器温度 | °C | 4 | 过热器平均温度 |
| 发电机功率 | MW | 8 | 发电机输出功率 |
| 主蒸汽压力 | MPa | 12 | 主蒸汽管道压力 |
| 总风量 | m³/h | 16 | 送入炉膛的总风量 |
| 水煤比 | - | 24 | 给水流量与燃煤量的比值 |
| 一次分机A导叶 | % | 28 | 一次风机A入口导叶开度 |
| 一次分机B导叶 | % | 32 | 一次风机B入口导叶开度 |
| 送风机A导叶 | % | 36 | 送风机A入口导叶开度 |
| 送风机B导叶 | % | 40 | 送风机B入口导叶开度 |
| 引风机A导叶 | % | 44 | 引风机A入口导叶开度 |
| 引风机B导叶 | % | 48 | 引风机B入口导叶开度 |
| CO浓度 | ppm | 52 | 烟气中一氧化碳浓度 |
| O2浓度 | % | 56 | 烟气中氧气浓度 |
| SO2浓度 | ppm | 60 | 烟气中二氧化硫浓度 |
| NOx浓度 | ppm | 64 | 烟气中氮氧化物浓度 |

## 错误处理

### 常见错误情况
1. **串口连接失败**：检查串口配置和物理连接
2. **无响应**：检查设备地址和通信参数
3. **CRC校验失败**：检查数据传输完整性
4. **报文格式错误**：验证报文头部和长度

### 调试信息
程序提供详细的调试输出，包括：
- 发送的请求报文
- 接收的响应数据
- 解析后的参数值
- 错误状态信息

## 注意事项

1. **字节序**：DCS 使用大端序，需要转换为小端序
2. **数据同步**：使用互斥锁保证多线程数据安全
3. **采集频率**：根据 `CollectionInterval` 配置调整采集间隔
4. **错误恢复**：通信失败时自动重试机制
5. **数据验证**：检查浮点数的有效性和合理性

## 相关文件

- `dcs.cpp` / `dcs.h`：DCS 通信实现
- `config.ini`：配置文件
- `config_manager.cpp`：配置管理
- `csvfile.cpp`：数据记录
