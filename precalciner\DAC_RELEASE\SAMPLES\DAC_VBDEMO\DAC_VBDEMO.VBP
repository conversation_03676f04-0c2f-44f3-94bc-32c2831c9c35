Type=Exe
Reference=*\G{00020430-0000-0000-C000-000000000046}#2.0#0#C:\WINNT\System32\stdole2.tlb#OLE Automation
Object={F9043C88-F6F2-101A-A3C9-08002B2F49FB}#1.2#0; COMDLG32.OCX
Object={831FDD16-0C5C-11D2-A9FC-0000F8754DA1}#2.0#0; MSCOMCTL.OCX
Module=Module1; Module1.bas
Form=frmMain.frm
Form=frmAbout.frm
Module=DACLTSDK; DACLTSDK.bas
Form=frmServerBrowser.frm
Form=frmItemBrowser.frm
Form=frmItemWrite.frm
Form=frmServerStatus.frm
Form=frmGroupStatus.frm
Form=frmItemStatus.frm
IconForm="frmMain"
Startup="frmMain"
HelpFile=""
NoControlUpgrade=1
Title="DAC_VBDEMO"
ExeName32="DAC_VBDEMO.exe"
Command32=""
Name="DAC_VBDEMO"
HelpContextID="0"
CompatibleMode="0"
MajorVer=1
MinorVer=0
RevisionVer=0
AutoIncrementVer=0
ServerSupportFiles=0
VersionComments="http://www.agilewill.com"
VersionCompanyName="agilewill"
VersionFileDescription="Demo OPC Client application in VB"
VersionLegalCopyright="agilewill"
VersionLegalTrademarks="agilewill"
VersionProductName="DACLTSDK"
CompilationType=0
OptimizationType=0
FavorPentiumPro(tm)=0
CodeViewDebugInfo=-1
NoAliasing=0
BoundsCheck=0
OverflowCheck=0
FlPointCheck=0
FDIVCheck=0
UnroundedFP=0
StartMode=0
Unattended=0
Retained=0
ThreadPerObject=0
MaxNumberOfThreads=1
DebugStartupOption=0

[MS Transaction Server]
AutoRefresh=1
