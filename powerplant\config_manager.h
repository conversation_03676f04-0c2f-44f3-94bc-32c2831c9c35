#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include <string>
#include <unordered_map>
#include <shared_mutex>
#include <mutex>
#include <fstream>
#include <sstream>
#include <iostream>
#include <stdexcept>
#include <vector>
#ifdef _WIN32
    #include <direct.h>
    #include <io.h>
#else
    #include <sys/stat.h>
    #include <unistd.h>
#endif

// 配置管理器类 - 支持读写锁保护的INI格式配置文件
class ConfigManager {
private:
    // 配置数据结构: 节名 -> (键名 -> 值)
    using Section = std::unordered_map<std::string, std::string>;
    std::unordered_map<std::string, Section> config_data;

    // 配置文件路径
    std::string file_path;

    // 互斥锁保护配置数据
    mutable std::mutex rw_mutex;

    // 辅助函数: 去除字符串两端空白
    static std::string trim(const std::string& str);

    // 辅助函数: 解析配置行
    static bool parse_line(const std::string& line, std::string& section, std::string& key, std::string& value);

public:
    // 构造函数
    explicit ConfigManager(const std::string& path);

    // 读取配置文件
    bool load();

    // 保存配置文件
    bool save();

    // 获取配置值（读操作）
    template<typename T>
    T get(const std::string& section, const std::string& key, const T& default_value = T()) const;

    // 设置配置值（写操作）
    template<typename T>
    void set(const std::string& section, const std::string& key, const T& value);

    // 检查配置项是否存在
    bool exists(const std::string& section, const std::string& key) const;

    // 删除配置项
    bool remove(const std::string& section, const std::string& key);

    // 删除整个节
    bool remove_section(const std::string& section);

    // 获取所有节名
    std::vector<std::string> get_sections() const;

    // 获取指定节中的所有键
    std::vector<std::string> get_keys(const std::string& section) const;

    // 重载流操作符，用于调试
    friend std::ostream& operator<<(std::ostream& os, const ConfigManager& config);
};

// 模板函数实现

// 获取配置值（读操作）
template<typename T>
T ConfigManager::get(const std::string& section, const std::string& key, const T& default_value) const {
    std::lock_guard<std::mutex> lock(rw_mutex);

    auto sec_it = config_data.find(section);
    if (sec_it == config_data.end()) {
        return default_value;
    }

    auto key_it = sec_it->second.find(key);
    if (key_it == sec_it->second.end()) {
        return default_value;
    }

    // 尝试将字符串转换为目标类型
    T result;
    std::istringstream iss(key_it->second);
    iss >> result;

    // 检查转换是否成功
    if (iss.fail()) {
        return default_value;
    }

    return result;
}

// 特化bool类型的获取方法
template<>
inline bool ConfigManager::get<bool>(const std::string& section, const std::string& key, const bool& default_value) const {
    std::lock_guard<std::mutex> lock(rw_mutex);

    auto sec_it = config_data.find(section);
    if (sec_it == config_data.end()) {
        return default_value;
    }

    auto key_it = sec_it->second.find(key);
    if (key_it == sec_it->second.end()) {
        return default_value;
    }

    const std::string& value = key_it->second;
    if (value == "true" || value == "True" || value == "TRUE" || value == "1") {
        return true;
    } else if (value == "false" || value == "False" || value == "FALSE" || value == "0") {
        return false;
    }

    return default_value;
}

// 设置配置值（写操作）
template<typename T>
void ConfigManager::set(const std::string& section, const std::string& key, const T& value) {
    std::lock_guard<std::mutex> lock(rw_mutex);

    std::ostringstream oss;
    oss << value;

    config_data[section][key] = oss.str();
}

#endif // CONFIG_MANAGER_H
