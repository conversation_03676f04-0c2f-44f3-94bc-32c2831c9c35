@echo off
chcp 65001 >nul
echo ========================================
echo Build PrecalcinerBurning 32-bit version
echo ========================================

echo Setting 32-bit Qt environment variables...
set QT32_PATH=D:\Development\Qt32\5.12 MinGW32\Qt\5.12.0\mingw73_32
set MINGW32_PATH=D:\Development\Qt32\5.12 MinGW32\Qt\Tools\mingw730_32\bin
set PATH=%QT32_PATH%\bin;%MINGW32_PATH%;C:\Windows\System32;C:\Windows

echo Current directory: %CD%
echo Qt path: %QT32_PATH%
echo MinGW32 path: %MINGW32_PATH%

echo.
echo Cleaning old build files...
if exist build_32bit_debug rmdir /s /q build_32bit_debug
mkdir build_32bit_debug
cd build_32bit_debug

echo.
echo Step 1: Running qmake to generate Makefile...
qmake ..\PrecalcinerBurning.pro -spec win32-g++ "CONFIG+=debug" "CONFIG+=qml_debug"

if %ERRORLEVEL% neq 0 (
    echo ERROR: qmake failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Building the project...
mingw32-make

if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Copying necessary DLL files...
copy "..\DACLTSDK.dll" "debug\"
copy "..\borlndmm.dll" "debug\"
copy "..\config.ini" "debug\"

echo.
echo Step 3.5: Copying additional runtime DLLs...
copy "%MINGW32_PATH%\libgcc_s_dw2-1.dll" "debug\" 2>nul
copy "%MINGW32_PATH%\libstdc++-6.dll" "debug\" 2>nul
copy "%MINGW32_PATH%\libwinpthread-1.dll" "debug\" 2>nul

echo.
echo Step 3.6: Copying Qt platform plugins...
if not exist "debug\platforms" mkdir "debug\platforms"
copy "%QT32_PATH%\plugins\platforms\qwindowsd.dll" "debug\platforms\" 2>nul

echo.
echo Step 4: Running windeployqt to copy Qt dependencies (optimized for debug)...
windeployqt --debug --no-translations --no-system-d3d-compiler --qmldir .. debug\PrecalcinerBurning.exe

echo.
echo Step 5: Cleaning up unnecessary files to reduce debug build size...
echo Removing unnecessary image format plugins...
if exist "debug\imageformats\qicns.dll" del "debug\imageformats\qicns.dll"
if exist "debug\imageformats\qtga.dll" del "debug\imageformats\qtga.dll"
if exist "debug\imageformats\qtiff.dll" del "debug\imageformats\qtiff.dll"
if exist "debug\imageformats\qwbmp.dll" del "debug\imageformats\qwbmp.dll"
if exist "debug\imageformats\qwebp.dll" del "debug\imageformats\qwebp.dll"

echo Removing unnecessary bearer plugins...
if exist "debug\bearer" rmdir /s /q "debug\bearer"

echo Removing unnecessary platform input contexts...
if exist "debug\platforminputcontexts" rmdir /s /q "debug\platforminputcontexts"

echo Removing translation files...
if exist "debug\translations" rmdir /s /q "debug\translations"

echo Note: Keeping QML debugging tools for development...

echo.
echo ========================================
echo DEBUG build completed successfully!
echo ========================================
echo Executable: build_32bit_debug\debug\PrecalcinerBurning.exe
echo.
echo Size comparison:
powershell -Command "Get-ChildItem 'debug' -Recurse | Measure-Object -Property Length -Sum | ForEach-Object { 'Total size: ' + [math]::Round($_.Sum/1MB,2) + ' MB' }"
echo ========================================

cd ..
pause