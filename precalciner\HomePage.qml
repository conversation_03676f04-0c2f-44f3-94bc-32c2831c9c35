import QtQuick 2.12
import QtQuick.Controls 2.5
import QtQuick.Layouts 1.12

Page {
    id: homePage
    
    // 信号定义
    signal navigateToMonitoring()
    signal navigateToDataScreen()
    signal navigateToParameterAdjustment()

    signal navigateToCollectionConfig()
    
    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }
    
    ColumnLayout {
        anchors.centerIn: parent
        spacing: 40
        
        Label {
            text: "水泥厂分解炉\n智慧燃烧系统"
            font.pixelSize: 48
            font.bold: true
            color: "#ffffff"
            Layout.alignment: Qt.AlignHCenter
            horizontalAlignment: Text.AlignHCenter
        }
        
        Button {
            text: "多维智慧燃烧控制系统"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 300
            Layout.preferredHeight: 80
            onClicked: homePage.navigateToDataScreen()

            background: Rectangle {
                color: parent.pressed ? "#ff6f00" : "#ff9800"
                radius: 10
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 24
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }

        Button {
            text: "分解炉燃烧数据监控系统"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 300
            Layout.preferredHeight: 80
            onClicked: homePage.navigateToMonitoring()

            background: Rectangle {
                color: parent.pressed ? "#388e3c" : "#4caf50"
                radius: 10
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 24
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }

        Button {
            text: "参数调整"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 300
            Layout.preferredHeight: 80
            onClicked: homePage.navigateToParameterAdjustment()

            background: Rectangle {
                color: parent.pressed ? "#e65100" : "#ff5722"
                radius: 10
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 24
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }



        Button {
            text: "采集配置"
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredWidth: 300
            Layout.preferredHeight: 80
            onClicked: homePage.navigateToCollectionConfig()

            background: Rectangle {
                color: parent.pressed ? "#795548" : "#8d6e63"
                radius: 10
            }

            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 24
                font.bold: true
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
        }
    }
}
