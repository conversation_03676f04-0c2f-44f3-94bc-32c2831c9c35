<RCC>
    <qresource prefix="/">
        <file>main.qml</file>
        <file>HomePage.qml</file>
        <file>ModelManagement.qml</file>
        <file>ModelSelection.qml</file>
        <file>PasswordInput.qml</file>
        <file>SecondPasswordInput.qml</file>
        <file>ModelConfirmation.qml</file>
        <file>ModelLoading.qml</file>
        <file>ModelCompletion.qml</file>
        <file>MonitoringSystem.qml</file>
        <file>DataScreenView.qml</file>
        <file>VideoView.qml</file>
        <file>VideoPlayerWidget.qml</file>
        <file>ParameterAdjustmentView.qml</file>
        <file>CalendarDataView.qml</file>
        <file>CollectionConfigView.qml</file>
    </qresource>
    <qresource prefix="/images">
        <file alias="app-icon.ico">images/app-icon.ico</file>
        <file alias="app-icon.png">images/app-icon.ico</file>
        <file alias="backhome.png">images/backhome.png</file>
        
        <file alias="center-CO.png">images/center-CO.png</file>
        <file alias="center-CO2.png">images/center-CO2.png</file>
        <file alias="center-NOx.png">images/center-NOx.png</file>
        <file alias="center-SO2.png">images/center-SO2.png</file>
        <file alias="center-temperature.png">images/center-temperature.png</file>
        <file alias="center-air-quantity.png">images/center-air-quantity.png</file>
        <file alias="center-fire.gif">images/center-fire.gif</file>
        <file alias="center-fuel-quantity.png">images/center-fuel-quantity.png</file>
        <file alias="center-main-pressure.png">images/center-main-pressure.png</file>
        <file alias="center-oxygen-content.png">images/center-oxygen-content.png</file>
        <file alias="center-payload.png">images/center-payload.png</file>
        <file alias="center-tool-fire.png">images/center-tool-fire.png</file>
        <file alias="rt-side-bot.png">images/rt-side-bot.png</file>
        <file alias="rt-side-top.png">images/rt-side-top.png</file>
        <file alias="side-box.png">images/side-box.png</file>
        <file alias="top-btn-active.png">images/top-btn-active.png</file>
        <file alias="top-btn.png">images/top-btn.png</file>
        <file alias="top-title-name.png">images/top-title-name.png</file>
        <file alias="top-title.png">images/top-title.png</file>
        <file alias="virtual-bg.png">images/virtual-bg.png</file>
    </qresource>
    <qresource prefix="/data">
        <!-- CSV数据文件将在运行时动态创建，这里预留资源路径 -->
    </qresource>
</RCC>
