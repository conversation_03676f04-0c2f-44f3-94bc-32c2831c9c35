import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: modelSelection
    
    property string selectedModel: ""
    
    // 信号定义
    signal navigateBack()
    signal navigateToPassword(string targetModel)
    
    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回"
                onClicked: modelSelection.navigateBack()
            }
            Label {
                text: "选择新模型"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }
        }
    }
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 40
        spacing: 30
        
        ListView {
            id: selectionListView
            Layout.fillWidth: true
            Layout.fillHeight: true
            model: modelManager.availableModels
            spacing: 15
            delegate: Rectangle {
                width: parent.width
                height: 120
                color: selectedModel === modelData ? "#E3F2FD" : "#ffffff"
                border.color: selectedModel === modelData ? "#E3F2FD" : "#e0e0e0"
                border.width: 1
                radius: 4

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        if (modelData !== modelManager.currentModel) {
                            selectedModel = modelData
                        }
                    }
                }

                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 20

                    RadioButton {
                        checked: selectedModel === modelData
                        enabled: modelData !== modelManager.currentModel
                        onClicked: {
                            if (modelData !== modelManager.currentModel) {
                                selectedModel = modelData
                            }
                        }
                    }

                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8

                        Label {
                            text: modelData
                            font.pixelSize: 20
                            font.bold: true
                            color: modelData === modelManager.currentModel ? "#999" : "#000000"
                        }

                        Label {
                            text: modelManager.getModelDescription(modelData)
                            font.pixelSize: 16
                            color: modelData === modelManager.currentModel ? "#999" : "#000000"
                            wrapMode: Text.WordWrap
                            Layout.fillWidth: true
                        }

                        Label {
                            text: modelData === modelManager.currentModel ? "（当前使用中）" : ""
                            font.pixelSize: 14
                            color: "#999"
                            visible: modelData === modelManager.currentModel
                        }
                    }
                }
            }
        }
        
        RowLayout {
            Layout.alignment: Qt.AlignHCenter
            spacing: 30

            Button {
                text: "取消"
                Layout.preferredWidth: 150
                Layout.preferredHeight: 50
                onClicked: modelSelection.navigateBack()

                background: Rectangle {
                    color: parent.pressed ? "#f0f0f0" : "#ffffff"
                    border.color: "#ddd"
                    border.width: 1
                    radius: 6
                }

                contentItem: Text {
                    text: parent.text
                    color: "#333"
                    font.pixelSize: 18
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            Button {
                text: "确认"
                Layout.preferredWidth: 150
                Layout.preferredHeight: 50
                enabled: selectedModel !== "" && selectedModel !== modelManager.currentModel
                onClicked: {
                    if (selectedModel !== "") {
                        modelSelection.navigateToPassword(selectedModel)
                    }
                }

                background: Rectangle {
                    color: parent.enabled ? (parent.pressed ? "#1976d2" : "#2196f3") : "#cccccc"
                    radius: 6
                }

                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 18
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }
}
