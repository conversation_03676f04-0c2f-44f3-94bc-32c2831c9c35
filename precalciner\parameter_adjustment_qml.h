#ifndef PARAMETER_ADJUSTMENT_QML_H
#define PARAMETER_ADJUSTMENT_QML_H

#include <QObject>
#include <QString>
#include <QVariantMap>

// QML包装类，用于在QML中调用参数调整功能
class ParameterAdjustmentQML : public QObject
{
    Q_OBJECT

public:
    explicit ParameterAdjustmentQML(QObject *parent = nullptr);

public slots:
    // 启用/禁用自动调整
    void enableAutoAdjustment(bool enable);
    
    // 获取调整建议（手动模式）
    // 返回值：是否需要调整
    bool getAdjustmentSuggestion(const QString& dcsName, 
                               float* suggestedValue, 
                               float* currentDiff,
                               QString* reason);
    
    // 执行手动调整
    bool executeManualAdjustment(const QString& dcsName, float newValue);
    
    // 获取自动调整状态
    bool isAutoAdjustmentActive();
    
    // 便捷函数：获取当前DCS的生料量调整建议信息
    Q_INVOKABLE QVariantMap getCurrentAdjustmentInfo(const QString& dcsName);

    // 便捷函数：获取给煤量调整建议信息
    Q_INVOKABLE QVariantMap getCoalFeedAdjustmentInfo(const QString& dcsName);

    // 获取当前调整建议（统一接口）
    Q_INVOKABLE QVariantMap getCurrentAdjustmentSuggestion();



    // 便捷函数：获取引风机转速调整建议信息
    Q_INVOKABLE QVariantMap getInducedDraftFanAdjustmentInfo(const QString& dcsName);

    // 便捷函数：执行自动调整模式切换
    Q_INVOKABLE void toggleAutoAdjustment(bool enable);

    // 便捷函数：执行手动调整并返回结果
    Q_INVOKABLE bool performManualAdjustment(const QString& dcsName, float newValue);

    // 设置用户可调参数
    Q_INVOKABLE void setFurnacePressureSetpoint(float pressure);
    Q_INVOKABLE void setOxygenConcentrationSetpoint(float concentration);

    // 获取用户可调参数
    Q_INVOKABLE float getFurnacePressureSetpoint();
    Q_INVOKABLE float getOxygenConcentrationSetpoint();

    // 设置和获取临时参数
    Q_INVOKABLE void setTempFurnaceSetTemp(float temp);
    Q_INVOKABLE void setTempPlannedRawMaterial(float material);
    Q_INVOKABLE float getTempFurnaceSetTemp();
    Q_INVOKABLE float getTempPlannedRawMaterial();

    // 获取当前DCS参数值 - 仅返回参数调整页面需要的3个参数
    Q_INVOKABLE QVariantMap getCurrentDCSValues(const QString& dcsName);

    // 获取DCS设备的采集间隔（秒）
    Q_INVOKABLE int getDCSCollectionInterval(const QString& dcsName);

    // 设置和获取生料量设定目标选择
    Q_INVOKABLE void setRawMaterialSetpointTarget(int target);
    Q_INVOKABLE int getRawMaterialSetpointTarget();

    // 获取当前DCS设定值
    Q_INVOKABLE QVariantMap getCurrentDCSSetValues(const QString& dcsName);

    // 获取生料量设定调整建议（同时包含两个生料量设定的建议）
    Q_INVOKABLE QVariantMap getRawMaterialSetSuggestion();

signals:
    // 调整状态变化信号
    void autoAdjustmentStatusChanged(bool enabled);
    
    // 调整执行完成信号
    void adjustmentExecuted(const QString& dcsName, float newValue, bool success);
    
    // 调整建议更新信号
    void adjustmentSuggestionUpdated(const QString& dcsName, bool needAdjustment, float suggestedValue);
};

#endif // PARAMETER_ADJUSTMENT_QML_H
