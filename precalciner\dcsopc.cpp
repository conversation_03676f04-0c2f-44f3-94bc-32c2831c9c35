/**
 * @file dcsopc.cpp
 * @brief OPC DA客户端实现文件 - 使用DACLTSDK与DCS系统进行OPC通信
 * @details 实现OPC DA (Data Access) 协议客户端，支持数据读取和写入
 *          使用DACLTSDK.dll封装库替代原有的COM接口实现
 *          与原有Modbus通信方式并行，通过config.ini配置选择通信方式
 */

#include "dcsopc.h"
#include "smoke_analyzer_comm.h"
#include "csvfile.h"
#include "boiler.h"
#include "parameter_adjustment.h"
#include <thread>
#include <chrono>
#include <cstring>
#include <unordered_map>

// 包含DACLTSDK头文件
#ifdef _WIN32
#include "DACLTSDK.h"
#include <windows.h>
#include <oleauto.h>
#include <psapi.h>  // 用于内存监控
#else
#include <unistd.h>
#endif

// 定义静态常量成员
const int DCSOPCDevice::MAX_RETRY_COUNT = 3;           // 最大重连次数
const int DCSOPCDevice::RETRY_INTERVAL_SECONDS = 60;   // 重连间隔（秒）

// 全局变量定义
// OPC设备映射表 - 存储所有OPC设备实例，供其他模块访问
std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

// 声明在smoke_analyzer_comm.cpp中定义的函数
extern std::vector<std::string> stringSplit(const std::string& str, char delim);

/**
 * @brief DCSOPCDevice构造函数
 * @param config_manager 配置管理器指针，用于读取config.ini配置
 * @param dcs_name DCS设备名称，对应config.ini中的配置段名（如"DCS1"）
 * @details 构造函数负责：
 *          1. 从config.ini读取OPC服务器配置和标签映射
 *          2. 初始化DACLTSDK
 *          3. 连接OPC服务器
 *          4. 创建OPC组和项目
 */
DCSOPCDevice::DCSOPCDevice(ConfigManager *config_manager, std::string dcs_name)
    : config_manager(config_manager), dcs_name(dcs_name),
      raw_material_data_valid(false), adjustment_interval_count(0), raw_material_diff_threshold(10.0f),
      is_initialized(false), connect_retry_count(0)
#ifdef _WIN32
    , opc_server_handle(0), opc_group_handle(0),
      opc_connected(false), sdk_initialized(false), com_initialized(false)
#endif
{
    // 初始化所有数据值为0 - 分解炉相关参数
    furnace_set_temp = furnace_actual_temp = actual_furnace_pressure = 0.0f;
    coal_feed_rate = actual_raw_material1 = actual_raw_material2 = 0.0f;
    planned_raw_material = induced_draft_fan_speed = 0.0f;

    // 初始化新增的四个设定值
    coal_feed_set = raw_material1_set = raw_material2_set = 0.0f;
    induced_draft_fan_set = 0.0f;

    // 初始化重连控制时间
    last_connect_attempt = std::chrono::steady_clock::now() - std::chrono::seconds(RETRY_INTERVAL_SECONDS);

#ifdef _WIN32
    // 32位程序内存优化：初始化COM环境（单线程模式，避免多线程COM问题）
    HRESULT hr = CoInitialize(NULL);
    if (SUCCEEDED(hr)) {
        com_initialized = true;
        debug_printf("DCS OPC设备 '%s' COM环境初始化成功\n", dcs_name.c_str());
    } else {
        debug_printf("DCS OPC设备 '%s' COM环境初始化失败: 0x%08X\n", dcs_name.c_str(), hr);
    }
#endif

    // 从config.ini加载OPC配置参数
    // 尝试加载配置并连接OPC服务器
    if (load_config() == 0) {
        is_initialized = true;
        debug_printf("DCS OPC设备 '%s' 初始化成功\n", dcs_name.c_str());
    } else {
        debug_printf("DCS OPC设备 '%s' 初始化失败\n", dcs_name.c_str());
    }
}

/**
 * @brief DCSOPCDevice析构函数
 * @details 负责清理资源：断开OPC连接、清理DACLTSDK
 */
DCSOPCDevice::~DCSOPCDevice() {
    debug_printf("DCS OPC设备 '%s' 开始析构\n", dcs_name.c_str());

    // 安全断开OPC连接
    try {
        disconnect_opc_server();
    } catch (...) {
        debug_printf("DCS OPC设备 '%s' 析构时断开连接发生异常\n", dcs_name.c_str());
    }

#ifdef _WIN32
    // 清理COM环境（32位程序内存优化）
    if (com_initialized) {
        CoUninitialize();
        com_initialized = false;
        debug_printf("DCS OPC设备 '%s' COM环境已清理\n", dcs_name.c_str());
    }
#endif

    debug_printf("DCS OPC设备 '%s' 析构完成\n", dcs_name.c_str());
}

/**
 * @brief 从config.ini加载OPC配置参数
 * @return 0-成功，-1-失败
 * @details 加载内容包括：
 *          1. 基本配置（描述、协议、采集间隔等）
 *          2. OPC服务器配置（ProgID、主机、更新频率等）
 *          3. OPC标签映射（读取标签和写入标签）
 */
int DCSOPCDevice::load_config() {
    // 读取基本配置信息
    desc = config_manager->get<std::string>(dcs_name, "Desc", "");
    protocol = config_manager->get<std::string>(dcs_name, "Protocol", "");
    collection_interval = config_manager->get<int>(dcs_name, "CollectionInterval", 10);
    raw_material_collection_interval = config_manager->get<int>(dcs_name, "RawMaterialCollectionInterval", collection_interval);

    // 读取关联的锅炉名称 - 用于数据关联
    associated_boiler = config_manager->get<std::string>(dcs_name, "AssociatedBoiler", "");
    // 如果没有配置关联锅炉，则自动选择第一个锅炉
    if (associated_boiler.empty()) {
        std::string boiler_list = config_manager->get<std::string>("BoilerList", "list");
        if (!boiler_list.empty()) {
            size_t comma_pos = boiler_list.find(',');
            if (comma_pos != std::string::npos) {
                associated_boiler = boiler_list.substr(0, comma_pos);
            } else {
                associated_boiler = boiler_list;
            }
        }
    }

    // ==================== 读取OPC服务器配置 ====================
    // 这些配置决定了如何连接到OPC服务器，现场对接时需要修改
    opc_server_prog_id = config_manager->get<std::string>(dcs_name, "OPCServerProgID", "Freelance2000OPCServer.129.1");
    opc_server_host = config_manager->get<std::string>(dcs_name, "OPCServerHost", "***********");
    opc_update_rate = config_manager->get<int>(dcs_name, "OPCUpdateRate", 1000);
    opc_group_name = config_manager->get<std::string>(dcs_name, "OPCGroupName", "DCS_Group");

    // ==================== 读取OPC服务器发现配置 ====================
    // 支持自动发现和手动指定两种模式
    opc_auto_discovery = config_manager->get<bool>(dcs_name, "OPCAutoDiscovery", true);
    opc_server_class_id = config_manager->get<std::string>(dcs_name, "OPCServerClassID", "{F822DE8F-207C-11D1-BAD4-006097385129}");

    // 读取备用服务器ProgID列表
    std::string prog_id_list_str = config_manager->get<std::string>(dcs_name, "OPCServerProgIDList", "");
    if (!prog_id_list_str.empty()) {
        opc_server_prog_id_list = stringSplit(prog_id_list_str, ',');
        // 去除空格
        for (auto& prog_id : opc_server_prog_id_list) {
            prog_id.erase(0, prog_id.find_first_not_of(" \t"));
            prog_id.erase(prog_id.find_last_not_of(" \t") + 1);
        }
    }

    // ==================== 读取OPC数据读取标签配置 ====================
    // 这些标签用于从DCS系统读取实时数据，标签名需要根据现场实际情况修改
    opc_furnace_set_temp_tag = config_manager->get<std::string>(dcs_name, "OPCFurnaceSetTempTag", "");
    opc_furnace_actual_temp_tag = config_manager->get<std::string>(dcs_name, "OPCFurnaceActualTempTag", "");
    opc_actual_furnace_pressure_tag = config_manager->get<std::string>(dcs_name, "OPCActualFurnacePressureTag", "");
    opc_coal_feed_rate_tag = config_manager->get<std::string>(dcs_name, "OPCCoalFeedRateTag", "");
    opc_actual_raw_material1_tag = config_manager->get<std::string>(dcs_name, "OPCActualRawMaterial1Tag", "");
    opc_actual_raw_material2_tag = config_manager->get<std::string>(dcs_name, "OPCActualRawMaterial2Tag", "");
    opc_planned_raw_material_tag = config_manager->get<std::string>(dcs_name, "OPCPlannedRawMaterialTag", "");
    opc_induced_draft_fan_speed_tag = config_manager->get<std::string>(dcs_name, "OPCInducedDraftFanSpeedTag", "");

    // 新增四个设定值标签配置
    opc_coal_feed_set_tag = config_manager->get<std::string>(dcs_name, "OPCCoalFeedSetTag", "");
    opc_raw_material1_set_tag = config_manager->get<std::string>(dcs_name, "OPCRawMaterial1SetTag", "");
    opc_raw_material2_set_tag = config_manager->get<std::string>(dcs_name, "OPCRawMaterial2SetTag", "");
    opc_induced_draft_fan_set_tag = config_manager->get<std::string>(dcs_name, "OPCInducedDraftFanSetTag", "");

    // ==================== 读取OPC设定值写入标签配置 ====================
    // 这些标签用于向DCS系统写入设定值控制指令，用于参数自动调整功能
    opc_coal_feed_set_write_tag = config_manager->get<std::string>(dcs_name, "OPCCoalFeedSetWriteTag", "");
    opc_raw_material1_set_write_tag = config_manager->get<std::string>(dcs_name, "OPCRawMaterial1SetWriteTag", "");
    opc_raw_material2_set_write_tag = config_manager->get<std::string>(dcs_name, "OPCRawMaterial2SetWriteTag", "");
    opc_induced_draft_fan_set_write_tag = config_manager->get<std::string>(dcs_name, "OPCInducedDraftFanSetWriteTag", "");

    // 初始化生料量数据状态和时间戳
    raw_material_data_valid = false;
    last_raw_material_update = std::chrono::steady_clock::now();

    // 加载参数调整相关配置（这些配置在OPC模式下仍然使用）
    raw_material_diff_threshold = config_manager->get<float>(dcs_name, "RawMaterialDiffThreshold", 10.0f);
    planned_raw_material_write_address = config_manager->get<int>(dcs_name, "PlannedRawMaterialWriteAddress", 100); // OPC模式下不使用，但保留兼容性
    adjustment_interval_count = 0;
    last_adjustment_time = std::chrono::steady_clock::now();

    // 输出配置加载结果到调试日志
    debug_printf("DCS OPC设备 '%s' 配置 - 协议: %s, 主要参数采集间隔: %d秒, 生料量采集间隔: %d秒\n",
           dcs_name.c_str(), protocol.c_str(), collection_interval, raw_material_collection_interval);
    debug_printf("DCS OPC设备 '%s' OPC配置 - 服务器: %s, 主机: %s, 更新频率: %dms\n",
           dcs_name.c_str(), opc_server_prog_id.c_str(), opc_server_host.c_str(), opc_update_rate);
    debug_printf("DCS OPC设备 '%s' OPC发现配置 - 自动发现: %s, ClassID: %s\n",
           dcs_name.c_str(), opc_auto_discovery ? "启用" : "禁用", opc_server_class_id.c_str());
    if (!opc_server_prog_id_list.empty()) {
        debug_printf("DCS OPC设备 '%s' 备用服务器列表: ", dcs_name.c_str());
        for (size_t i = 0; i < opc_server_prog_id_list.size(); i++) {
            debug_printf("%s%s", opc_server_prog_id_list[i].c_str(),
                        (i < opc_server_prog_id_list.size() - 1) ? ", " : "\n");
        }
    }
    debug_printf("DCS OPC设备 '%s' 参数调整配置 - 生料量差值阈值: %.1fkg\n",
           dcs_name.c_str(), raw_material_diff_threshold);

    return 0; // 配置加载成功
}

#ifdef _WIN32
/**
 * @brief 字符串编码转换辅助函数 - 宽字符转UTF8
 * @param wstr 宽字符串
 * @return 转换后的UTF8编码字符串
 * @details 用于将OPC返回的宽字符串转换为程序内部使用的UTF8字符串
 */
std::string DCSOPCDevice::wstring_to_string(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

/**
 * @brief 连接到OPC服务器
 * @return 0-成功，-1-失败
 * @details 连接过程包括：
 *          1. 初始化DACLTSDK
 *          2. 初始化COM环境
 *          3. 发现并连接OPC服务器
 *          4. 创建OPC组
 *          5. 添加OPC项目（标签）
 */
int DCSOPCDevice::connect_opc_server() {
    // 检查是否已经连接
    if (opc_connected) {
        debug_printf("DCS OPC设备 '%s' 已经连接到OPC服务器\n", dcs_name.c_str());
        return 0;
    }

    debug_printf("DCS OPC设备 '%s' 开始连接OPC服务器，主机: %s\n", dcs_name.c_str(), opc_server_host.c_str());

    // 步骤1：初始化DACLTSDK
    if (!sdk_initialized) {
        debug_printf("DCS OPC设备 '%s' 初始化DACLTSDK...\n", dcs_name.c_str());
        if (!InitOPCDef()) {
            debug_printf("DCS OPC设备 '%s' DACLTSDK初始化失败\n", dcs_name.c_str());
            return -1;
        }
        sdk_initialized = true;
        debug_printf("DCS OPC设备 '%s' DACLTSDK初始化成功\n", dcs_name.c_str());
    }

    // 步骤2：检查COM环境（已在构造函数中初始化）
    if (!com_initialized) {
        debug_printf("DCS OPC设备 '%s' COM环境未初始化，无法连接OPC服务器\n", dcs_name.c_str());
        return -1;
    }

    // 步骤3：初始化OPC客户端
    debug_printf("DCS OPC设备 '%s' 初始化OPC客户端...\n", dcs_name.c_str());
    if (!ASDAC_Init()) {
        debug_printf("DCS OPC设备 '%s' OPC客户端初始化失败\n", dcs_name.c_str());
        return -1;
    }
    debug_printf("DCS OPC设备 '%s' OPC客户端初始化成功\n", dcs_name.c_str());

    // 步骤4：根据配置选择连接方式
    if (opc_auto_discovery) {
        debug_printf("DCS OPC设备 '%s' 启用自动发现模式，正在发现 %s 上的OPC服务器...\n",
                    dcs_name.c_str(), opc_server_host.c_str());

        // 尝试自动发现并连接
        if (try_connect_with_discovery() == 0) {
            debug_printf("DCS OPC设备 '%s' 自动发现连接成功\n", dcs_name.c_str());
            return 0;
        } else {
            debug_printf("DCS OPC设备 '%s' 自动发现失败，尝试手动指定的服务器\n", dcs_name.c_str());
        }
    }

    // 步骤5：尝试手动指定的服务器连接
    debug_printf("DCS OPC设备 '%s' 尝试连接主要服务器: %s\n", dcs_name.c_str(), opc_server_prog_id.c_str());
    if (try_connect_to_server(opc_server_prog_id, opc_server_class_id) == 0) {
        debug_printf("DCS OPC设备 '%s' 主要服务器连接成功\n", dcs_name.c_str());
        return 0;
    }

    // 尝试备用服务器列表
    for (const auto& prog_id : opc_server_prog_id_list) {
        debug_printf("DCS OPC设备 '%s' 尝试备用服务器: %s\n", dcs_name.c_str(), prog_id.c_str());
        if (try_connect_to_server(prog_id, "") == 0) {
            debug_printf("DCS OPC设备 '%s' 备用服务器连接成功: %s\n", dcs_name.c_str(), prog_id.c_str());
            return 0;
        }
    }

    debug_printf("DCS OPC设备 '%s' 所有服务器连接尝试均失败\n", dcs_name.c_str());
    return -1;
}

/**
 * @brief 断开OPC服务器连接
 * @return 0-成功
 * @details 清理所有OPC资源：断开连接、清理DACLTSDK
 */
int DCSOPCDevice::disconnect_opc_server() {
    if (!opc_connected) {
        return 0;
    }

    debug_printf("DCS OPC设备 '%s' 开始断开OPC连接\n", dcs_name.c_str());

    cleanup_opc_resources();

    // 清理DACLTSDK
    if (sdk_initialized) {
        try {
            ASDAC_Uninit();
            FreeOPCDef();
            sdk_initialized = false;
            debug_printf("DCS OPC设备 '%s' DACLTSDK已清理\n", dcs_name.c_str());
        } catch (...) {
            debug_printf("DCS OPC设备 '%s' DACLTSDK清理时发生异常\n", dcs_name.c_str());
        }
    }

    // 注意：COM环境在析构函数中清理，这里不调用CoUninitialize()

    opc_connected = false;
    debug_printf("DCS OPC设备 '%s' OPC连接已断开\n", dcs_name.c_str());
    return 0;
}

// 清理OPC资源
void DCSOPCDevice::cleanup_opc_resources() {
    // 移除所有OPC项
    for (const auto& item : opc_item_handles) {
        if (item.second != 0) {
            ASDAC_RemoveItem(opc_server_handle, opc_group_handle, item.second);
        }
    }
    opc_item_handles.clear();

    // 移除OPC组
    if (opc_group_handle != 0) {
        ASDAC_RemoveGroup(opc_server_handle, opc_group_handle);
        opc_group_handle = 0;
    }

    // 断开OPC服务器连接
    if (opc_server_handle != 0) {
        ASDAC_Disconnect(opc_server_handle);
        opc_server_handle = 0;
    }
}

/**
 * @brief 尝试通过自动发现连接OPC服务器
 * @return 0-成功，-1-失败
 * @details 使用DACLTSDK的服务器发现功能：
 *          1. 枚举指定主机上的OPC服务器
 *          2. 尝试连接发现的服务器
 */
int DCSOPCDevice::try_connect_with_discovery() {
    debug_printf("DCS OPC设备 '%s' 开始自动发现OPC服务器，主机: %s\n", dcs_name.c_str(), opc_server_host.c_str());

    // 枚举指定主机上的OPC服务器
    auto discovered_servers = enumerate_opc_servers_on_host(opc_server_host);

    if (discovered_servers.empty()) {
        debug_printf("DCS OPC设备 '%s' 在主机 %s 上未发现任何OPC服务器\n", dcs_name.c_str(), opc_server_host.c_str());
        return -1;
    }

    debug_printf("DCS OPC设备 '%s' 发现 %zu 个OPC服务器:\n", dcs_name.c_str(), discovered_servers.size());
    for (size_t i = 0; i < discovered_servers.size(); i++) {
        debug_printf("  %zu. 服务器名称: %s, ClassID: %s\n",
                    i+1, discovered_servers[i].first.c_str(), discovered_servers[i].second.c_str());
    }

    // 尝试连接发现的每个服务器
    for (const auto& server : discovered_servers) {
        debug_printf("DCS OPC设备 '%s' 尝试连接发现的服务器: %s\n", dcs_name.c_str(), server.first.c_str());

        // 尝试连接服务器
        if (try_connect_to_server(server.first, server.second) == 0) {
            debug_printf("DCS OPC设备 '%s' 连接成功: %s\n", dcs_name.c_str(), server.first.c_str());
            return 0;
        }
    }

    debug_printf("DCS OPC设备 '%s' 所有发现的服务器连接均失败\n", dcs_name.c_str());
    return -1;
}

/**
 * @brief 枚举指定主机上的OPC服务器
 * @param host 主机IP地址或名称
 * @return 服务器列表，每个元素包含(ProgID, ClassID)
 * @details 使用DACLTSDK的ASDAC_GetServers函数枚举OPC DA服务器
 */
std::vector<std::pair<std::string, std::string>> DCSOPCDevice::enumerate_opc_servers_on_host(const std::string& host) {
    std::vector<std::pair<std::string, std::string>> servers;

    debug_printf("DCS OPC设备 '%s' 正在扫描 %s 上的OPC服务器...\n", dcs_name.c_str(), host.c_str());

    VARIANT serverNames, serverClassIDs;
    VariantInit(&serverNames);
    VariantInit(&serverClassIDs);

    // 调用ASDAC_GetServers发现服务器（添加异常处理）
    DWORD serverCount = 0;
    try {
        serverCount = ASDAC_GetServers(host.c_str(), 2, &serverNames, &serverClassIDs);
    } catch (...) {
        debug_printf("DCS OPC设备 '%s' 服务器发现时发生异常\n", dcs_name.c_str());
        VariantClear(&serverNames);
        VariantClear(&serverClassIDs);
        return servers;
    }

    if (serverCount == 0) {
        debug_printf("DCS OPC设备 '%s' 在 %s 上未发现任何OPC服务器\n", dcs_name.c_str(), host.c_str());
        VariantClear(&serverNames);
        VariantClear(&serverClassIDs);
        return servers;
    }

    // 处理VARIANT数组
    SAFEARRAY* pSANames = serverNames.parray;
    SAFEARRAY* pSAClassIDs = serverClassIDs.parray;

    BSTR* pbNames = NULL;
    BSTR* pbClassIDs = NULL;

    SafeArrayAccessData(pSANames, (void**)&pbNames);
    SafeArrayAccessData(pSAClassIDs, (void**)&pbClassIDs);

    for (DWORD i = 0; i < serverCount; i++) {
        // 转换BSTR到std::string
        int nameLen = WideCharToMultiByte(CP_UTF8, 0, pbNames[i], -1, NULL, 0, NULL, NULL);
        std::string serverName;
        if (nameLen > 0) {
            char* nameBuffer = new char[nameLen];
            WideCharToMultiByte(CP_UTF8, 0, pbNames[i], -1, nameBuffer, nameLen, NULL, NULL);
            serverName = nameBuffer;
            delete[] nameBuffer;
        }

        int classIdLen = WideCharToMultiByte(CP_UTF8, 0, pbClassIDs[i], -1, NULL, 0, NULL, NULL);
        std::string serverClassID;
        if (classIdLen > 0) {
            char* classIdBuffer = new char[classIdLen];
            WideCharToMultiByte(CP_UTF8, 0, pbClassIDs[i], -1, classIdBuffer, classIdLen, NULL, NULL);
            serverClassID = classIdBuffer;
            delete[] classIdBuffer;
        }

        servers.push_back(std::make_pair(serverName, serverClassID));
        debug_printf("DCS OPC设备 '%s' 发现服务器: %s (%s)\n",
                    dcs_name.c_str(), serverName.c_str(), serverClassID.c_str());
    }

    SafeArrayUnaccessData(pSANames);
    SafeArrayUnaccessData(pSAClassIDs);

    VariantClear(&serverNames);
    VariantClear(&serverClassIDs);

    debug_printf("DCS OPC设备 '%s' 服务器发现完成，共发现 %zu 个服务器\n", dcs_name.c_str(), servers.size());
    return servers;
}

/**
 * @brief 尝试连接到指定的OPC服务器
 * @param server_name 服务器的ProgID名称
 * @param server_class_id 服务器的ClassID
 * @return 0-成功，-1-失败
 * @details 使用DACLTSDK连接OPC服务器，包括创建组和添加项目
 */
int DCSOPCDevice::try_connect_to_server(const std::string& server_name, const std::string& server_class_id) {
    debug_printf("DCS OPC设备 '%s' 尝试连接服务器: %s\n", dcs_name.c_str(), server_name.c_str());

    // 清理之前的连接（如果有）
    cleanup_opc_resources();

    // 使用DACLTSDK连接OPC服务器
    std::string class_id_to_use = server_class_id.empty() ? opc_server_class_id : server_class_id;

    debug_printf("DCS OPC设备 '%s' 尝试连接 - 主机: %s, ClassID: %s\n",
                dcs_name.c_str(), opc_server_host.c_str(), class_id_to_use.c_str());

    try {
        opc_server_handle = ASDAC_Connect(opc_server_host.c_str(), class_id_to_use.c_str(), 2);
    } catch (...) {
        debug_printf("DCS OPC设备 '%s' 连接时发生异常\n", dcs_name.c_str());
        return -1;
    }

    if (opc_server_handle == 0) {
        debug_printf("DCS OPC设备 '%s' 连接OPC服务器失败: %s\n", dcs_name.c_str(), server_name.c_str());
        return -1;
    }

    debug_printf("DCS OPC设备 '%s' OPC服务器连接成功: %s (句柄: %lu)\n",
                dcs_name.c_str(), server_name.c_str(), opc_server_handle);

    // 创建OPC组
    if (create_opc_group() != 0) {
        debug_printf("DCS OPC设备 '%s' 创建OPC组失败\n", dcs_name.c_str());
        cleanup_opc_resources();
        return -1;
    }

    // 添加OPC项目
    if (add_opc_items() != 0) {
        debug_printf("DCS OPC设备 '%s' 添加OPC项失败\n", dcs_name.c_str());
        cleanup_opc_resources();
        return -1;
    }

    opc_connected = true;
    debug_printf("DCS OPC设备 '%s' OPC连接完全建立，服务器: %s\n", dcs_name.c_str(), server_name.c_str());
    return 0;
}
#endif

/**
 * @brief 启动主要参数数据采集线程
 * @details 创建独立线程执行do_data_collect函数，采集炉膛温度、压力、煤量等主要参数
 */
void DCSOPCDevice::start_data_collect() {
    std::thread t(&DCSOPCDevice::do_data_collect, this);
    t.detach(); // 分离线程，让其独立运行
}

/**
 * @brief 启动生料量独立采集线程（32位程序稳定性优化：改为空实现）
 * @details 为了提高32位程序稳定性，生料量数据现在由主采集线程统一采集
 *          保留此函数接口以维持兼容性，但不再启动独立线程
 */
void DCSOPCDevice::start_raw_material_collect() {
    debug_printf("DCS OPC设备 '%s' 32位程序优化：生料量采集已合并到主采集线程，不再启动独立线程\n", dcs_name.c_str());
    // 不再启动独立线程，生料量数据由do_data_collect统一采集
}

#ifdef _WIN32
/**
 * 创建OPC组
 * 使用DACLTSDK在OPC服务器中创建一个组，用于管理相关的OPC项
 * OPC组是OPC项的容器，提供统一的更新频率和管理接口
 *
 * @return 0表示成功，-1表示失败
 */
int DCSOPCDevice::create_opc_group() {
    // 检查OPC服务器连接状态
    if (opc_server_handle == 0) {
        debug_printf("DCS OPC设备 '%s' OPC服务器未连接\n", dcs_name.c_str());
        return -1;
    }

    debug_printf("DCS OPC设备 '%s' 创建OPC组: %s\n", dcs_name.c_str(), opc_group_name.c_str());

    // 使用DACLTSDK创建OPC组
    opc_group_handle = ASDAC_AddGroup(
        opc_server_handle,       // 服务器句柄
        opc_group_name.c_str(),  // 组名
        TRUE,                    // 激活状态
        opc_update_rate,         // 更新频率（毫秒）
        0,                       // 客户端句柄
        0.0f,                    // 死区
        0                        // 时区偏移
    );

    // 检查组创建结果
    if (opc_group_handle == 0) {
        debug_printf("DCS OPC设备 '%s' 创建OPC组失败\n", dcs_name.c_str());
        return -1;
    }

    debug_printf("DCS OPC设备 '%s' OPC组创建成功 (句柄: %lu)\n", dcs_name.c_str(), opc_group_handle);
    return 0;
}

/**
 * 添加OPC项到组中
 * 使用DACLTSDK将所有需要监控的DCS参数作为OPC项添加到之前创建的OPC组中
 * 每个OPC项对应一个具体的DCS参数（如温度、压力、流量等）
 *
 * @return 0表示成功，-1表示失败
 */
int DCSOPCDevice::add_opc_items() {
    // 检查OPC组是否已创建
    if (opc_group_handle == 0) {
        debug_printf("DCS OPC设备 '%s' OPC组未创建\n", dcs_name.c_str());
        return -1;
    }

    // 准备要添加的OPC项列表
    // 这些标签名对应DCS系统中的具体参数点位
    std::vector<std::string> tag_names = {
        opc_furnace_set_temp_tag,           // 炉膛设定温度（DCS待新建）
        opc_furnace_actual_temp_tag,        // 炉膛实际温度
        opc_actual_furnace_pressure_tag,    // 实际炉膛压力
        opc_coal_feed_rate_tag,             // 给煤量
        opc_actual_raw_material1_tag,       // 实际生料量1
        opc_actual_raw_material2_tag,       // 实际生料量2
        opc_planned_raw_material_tag,       // 计划生料量（DCS待新建）
        opc_induced_draft_fan_speed_tag,    // 引风机转速
        opc_coal_feed_set_tag,              // 给煤量设定
        opc_raw_material1_set_tag,          // 生料量1设定
        opc_raw_material2_set_tag,          // 生料量2设定
        opc_induced_draft_fan_set_tag       // 引风机转速设定
    };

    int success_count = 0;

    // 逐个添加OPC项
    for (const auto& tag_name : tag_names) {
        debug_printf("DCS OPC设备 '%s' 添加OPC项: %s\n", dcs_name.c_str(), tag_name.c_str());

        // 使用DACLTSDK添加OPC项
        DWORD item_handle = ASDAC_AddItem(opc_server_handle, opc_group_handle, tag_name.c_str());

        if (item_handle != 0) {
            // 保存项句柄到映射表，用于后续读写操作
            opc_item_handles[tag_name] = item_handle;
            debug_printf("DCS OPC设备 '%s' 添加OPC项成功: %s (句柄: %lu)\n",
                       dcs_name.c_str(), tag_name.c_str(), item_handle);

            // 激活项目以接收数据变化通知
            ASDAC_ActiveItem(opc_server_handle, opc_group_handle, item_handle, TRUE);
            success_count++;
        } else {
            debug_printf("DCS OPC设备 '%s' 添加OPC项失败: %s\n",
                       dcs_name.c_str(), tag_name.c_str());
        }
    }

    debug_printf("DCS OPC设备 '%s' OPC项添加完成，成功添加 %d 个项\n", dcs_name.c_str(), success_count);
    return success_count > 0 ? 0 : -1;
}

/**
 * 读取单个OPC项的值
 * 使用DACLTSDK读取指定标签的当前值
 *
 * @param tag_name 要读取的OPC项标签名
 * @param value 用于接收读取值的VARIANT变量
 * @return 0表示成功，-1表示失败
 */
int DCSOPCDevice::read_opc_item(const std::string& tag_name, VARIANT& value) {
    // 检查OPC连接状态
    if (!opc_connected || opc_server_handle == 0 || opc_group_handle == 0) {
        debug_printf("DCS OPC设备 '%s' OPC未连接，无法读取项: %s\n", dcs_name.c_str(), tag_name.c_str());
        return -1;
    }

    // 32位程序稳定性：检查标签名是否为空
    if (tag_name.empty()) {
        debug_printf("DCS OPC设备 '%s' 标签名为空，跳过读取\n", dcs_name.c_str());
        return -1;
    }

    // 查找OPC项句柄
    auto it = opc_item_handles.find(tag_name);
    if (it == opc_item_handles.end()) {
        debug_printf("DCS OPC设备 '%s' 找不到OPC项句柄: %s\n", dcs_name.c_str(), tag_name.c_str());
        return -1;
    }

    DWORD item_handle = it->second;          // 获取项句柄
    FILETIME timestamp;
    WORD quality;

    // 初始化VARIANT
    VariantInit(&value);

    // 使用DACLTSDK执行同步读取操作，添加异常保护（32位程序稳定性优化）
    try {
        BOOL read_result = FALSE;

        // 32位程序内存保护：在读取前检查句柄有效性
        if (opc_server_handle == 0 || opc_group_handle == 0 || item_handle == 0) {
            debug_printf("DCS OPC设备 '%s' 读取OPC项失败: 句柄无效 %s (server=%lu, group=%lu, item=%lu)\n",
                       dcs_name.c_str(), tag_name.c_str(), opc_server_handle, opc_group_handle, item_handle);
            VariantClear(&value);
            return -1;
        }

        // 32位程序稳定性：添加读取前的延迟，避免过于频繁的SDK调用
        std::this_thread::sleep_for(std::chrono::milliseconds(1));

        read_result = ASDAC_ReadItem(opc_server_handle, opc_group_handle, item_handle, &value, &timestamp, &quality);
        if (read_result) {
            // 读取成功
            return 0;
        } else {
            // 读取失败，但不一定是连接问题，可能是标签不存在或权限问题
            debug_printf("DCS OPC设备 '%s' 读取OPC项失败: %s (可能是标签问题，非连接问题)\n",
                       dcs_name.c_str(), tag_name.c_str());
            VariantClear(&value);
            return -1;
        }
    } catch (const std::exception& e) {
        debug_printf("DCS OPC设备 '%s' 读取OPC项时发生标准异常: %s, 异常信息: %s\n",
                   dcs_name.c_str(), tag_name.c_str(), e.what());
        VariantClear(&value);
        return -1;
    } catch (...) {
        debug_printf("DCS OPC设备 '%s' 读取OPC项时发生未知异常: %s (可能是连接断开或内存问题)\n",
                   dcs_name.c_str(), tag_name.c_str());
        VariantClear(&value);  // 确保清理VARIANT资源

        // 异常情况下，检查连接是否真的断开
        // 只有在严重异常时才重置连接状态
        // 注意：不要轻易重置opc_connected，因为可能只是单个标签的问题
        debug_printf("DCS OPC设备 '%s' 读取异常，但保持连接状态不变 (句柄: server=%d, group=%d)\n",
                   dcs_name.c_str(), opc_server_handle, opc_group_handle);
        return -1;
    }
}

/**
 * 写入单个OPC项的值
 * 使用DACLTSDK向指定标签写入新值
 * 主要用于参数调节时向DCS发送控制指令
 *
 * @param tag_name 要写入的OPC项标签名
 * @param value 要写入的值（VARIANT类型）
 * @return 0表示成功，-1表示失败
 */
int DCSOPCDevice::write_opc_item(const std::string& tag_name, const VARIANT& value) {
    // 检查OPC连接状态
    if (!opc_connected || opc_server_handle == 0 || opc_group_handle == 0) {
        debug_printf("DCS OPC设备 '%s' OPC未连接，无法写入项: %s\n", dcs_name.c_str(), tag_name.c_str());
        return -1;
    }

    // 查找OPC项句柄
    auto it = opc_item_handles.find(tag_name);
    if (it == opc_item_handles.end()) {
        debug_printf("DCS OPC设备 '%s' 找不到OPC项句柄: %s\n", dcs_name.c_str(), tag_name.c_str());
        return -1;
    }

    DWORD item_handle = it->second;      // 获取项句柄

    // 使用DACLTSDK执行同步写入操作
    if (ASDAC_WriteItem(opc_server_handle, opc_group_handle, item_handle, const_cast<VARIANT&>(value), TRUE)) {
        debug_printf("DCS OPC设备 '%s' 写入OPC项成功: %s\n", dcs_name.c_str(), tag_name.c_str());
        return 0;
    } else {
        debug_printf("DCS OPC设备 '%s' 写入OPC项失败: %s\n", dcs_name.c_str(), tag_name.c_str());
        return -1;
    }
}
#endif

/**
 * 写入浮点数值到OPC项
 * 这是一个便利函数，将float值包装成VARIANT后写入OPC项
 *
 * @param tag_name OPC项标签名
 * @param value 要写入的浮点数值
 * @return 0表示成功，-1表示失败
 */
int DCSOPCDevice::write_float_value(const std::string& tag_name, float value) {
#ifdef _WIN32
    // 创建并初始化VARIANT变量
    VARIANT var;
    VariantInit(&var);
    var.vt = VT_R4;        // 设置为32位浮点数类型
    var.fltVal = value;    // 设置浮点数值

    // 调用底层写入函数
    int result = write_opc_item(tag_name, var);

    // 清理VARIANT资源
    VariantClear(&var);
    return result;
#else
    debug_printf("DCS OPC设备 '%s' OPC功能仅在Windows平台支持\n", dcs_name.c_str());
    return -1;
#endif
}



/**
 * 采集DCS数据
 * 通过OPC接口读取所有配置的DCS参数
 * 这是数据采集的核心函数，定期被调用以获取最新的DCS数据
 *
 * @return 0表示成功（即使部分读取失败），-1表示完全失败
 */
int DCSOPCDevice::read_data() {
#ifdef _WIN32
    // 检查OPC连接状态 - 增强连接状态验证
    // 不仅检查opc_connected标志，还要验证句柄的有效性
    bool connection_valid = opc_connected && (opc_server_handle != 0) && (opc_group_handle != 0);

    if (!connection_valid) {
        // 如果标志显示已连接但句柄无效，重置连接状态
        if (opc_connected && (opc_server_handle == 0 || opc_group_handle == 0)) {
            debug_printf("DCS OPC设备 '%s' 检测到连接状态不一致，重置连接状态 (connected=%s, server_handle=%d, group_handle=%d)\n",
                       dcs_name.c_str(), opc_connected ? "true" : "false", opc_server_handle, opc_group_handle);
            opc_connected = false;
        }
        auto now = std::chrono::steady_clock::now();
        auto time_since_last_attempt = std::chrono::duration_cast<std::chrono::seconds>(
            now - last_connect_attempt).count();

        // 限制重连频率：60秒内不重复尝试
        if (time_since_last_attempt < RETRY_INTERVAL_SECONDS) {
            debug_printf("DCS OPC设备 '%s' 距离上次连接尝试不足%d秒，跳过重连\n",
                        dcs_name.c_str(), RETRY_INTERVAL_SECONDS);
            // 设置无效数据并返回
            std::lock_guard<std::mutex> lock(rwMutex);
            furnace_set_temp = furnace_actual_temp = actual_furnace_pressure = 0.0f;
            coal_feed_rate = actual_raw_material1 = actual_raw_material2 = 0.0f;
            planned_raw_material = induced_draft_fan_speed = 0.0f;
            // 重置新增的设定值
            coal_feed_set = raw_material1_set = raw_material2_set = 0.0f;
            induced_draft_fan_set = 0.0f;
            return 0;
        }

        // 限制重连次数
        if (connect_retry_count >= MAX_RETRY_COUNT) {
            debug_printf("DCS OPC设备 '%s' 已达到最大重连次数(%d次)，停止尝试重连\n",
                        dcs_name.c_str(), MAX_RETRY_COUNT);
            // 设置无效数据并返回
            std::lock_guard<std::mutex> lock(rwMutex);
            furnace_set_temp = furnace_actual_temp = actual_furnace_pressure = 0.0f;
            coal_feed_rate = actual_raw_material1 = actual_raw_material2 = 0.0f;
            planned_raw_material = induced_draft_fan_speed = 0.0f;
            // 重置新增的设定值
            coal_feed_set = raw_material1_set = raw_material2_set = 0.0f;
            induced_draft_fan_set = 0.0f;
            return 0;
        }

        // 记录重连尝试时间和次数
        last_connect_attempt = now;
        connect_retry_count++;

        debug_printf("DCS OPC设备 '%s' 开始第%d次重连尝试\n", dcs_name.c_str(), connect_retry_count);

        // 在重连前先完全清理现有资源，防止资源累积
        try {
            cleanup_opc_resources();

            // 强制垃圾回收COM对象（32位程序内存管理优化）
            CoFreeUnusedLibraries();

            // 短暂延迟，让系统完成资源清理
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

        } catch (...) {
            debug_printf("DCS OPC设备 '%s' 重连前资源清理时发生异常\n", dcs_name.c_str());
        }

        // 尝试重新连接OPC服务器
        if (connect_opc_server() != 0) {
            debug_printf("DCS OPC设备 '%s' 第%d次重连失败\n", dcs_name.c_str(), connect_retry_count);
            // 连接失败时设置所有数据为无效值
            std::lock_guard<std::mutex> lock(rwMutex);
            furnace_actual_temp = actual_furnace_pressure = 0.0f;
            coal_feed_rate = actual_raw_material1 = actual_raw_material2 = 0.0f;
            induced_draft_fan_speed = 0.0f;
            // 重置新增的设定值
            coal_feed_set = raw_material1_set = raw_material2_set = 0.0f;
            induced_draft_fan_set = 0.0f;
            return 0;  // 返回0以避免上层重复尝试
        } else {
            // 连接成功，重置重连计数器
            connect_retry_count = 0;
            debug_printf("DCS OPC设备 '%s' 重连成功，重置重连计数器\n", dcs_name.c_str());
        }
    }

    // 使用安全的VARIANT管理类（32位程序稳定性优化）
    SafeVariant safeValue;
    if (!safeValue.isValid()) {
        debug_printf("DCS OPC设备 '%s' VARIANT初始化失败\n", dcs_name.c_str());
        return 0;
    }
    VARIANT& value = *safeValue.get();

    // 临时变量存储读取的数据
    // 使用临时变量可以避免在读取过程中部分更新成员变量
    float temp_furnace_set_temp = 0.0f;
    float temp_furnace_actual_temp = 0.0f;
    float temp_actual_furnace_pressure = 0.0f;
    float temp_coal_feed_rate = 0.0f;
    float temp_actual_raw_material1 = 0.0f;
    float temp_actual_raw_material2 = 0.0f;
    float temp_planned_raw_material = 0.0f;
    float temp_induced_draft_fan_speed = 0.0f;

    // 新增四个设定值的临时变量
    float temp_coal_feed_set = 0.0f;
    float temp_raw_material1_set = 0.0f;
    float temp_raw_material2_set = 0.0f;
    float temp_induced_draft_fan_set = 0.0f;

    int success_count = 0;  // 成功读取的项目计数

    // 逐个读取所有DCS参数
    // 每次读取后都要检查数据类型和清理VARIANT资源

    // 使用SafeVariant自动管理VARIANT资源，简化清理逻辑（32位程序内存优化）
    auto cleanup_variant = [&safeValue]() {
        safeValue.clear();
    };

    // 32位程序稳定性：添加读取间隔，避免过于频繁的SDK调用导致内存问题
    const int READ_DELAY_MS = 2;

    // 读取炉膛设定温度（°C）- 检查标签是否为空（DCS待新建）
    if (!opc_furnace_set_temp_tag.empty()) {
        try {
            std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
            if (read_opc_item(opc_furnace_set_temp_tag, value) == 0) {
                if (value.vt == VT_R4) {  // 确认数据类型为32位浮点数
                    temp_furnace_set_temp = value.fltVal;
                    success_count++;
                }
            }
        } catch (...) {
            debug_printf("DCS OPC设备 '%s' 读取炉膛设定温度时发生异常\n", dcs_name.c_str());
        }
        cleanup_variant();
    }

    // 读取炉膛实际温度（°C）
    try {
        std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
        if (read_opc_item(opc_furnace_actual_temp_tag, value) == 0) {
            if (value.vt == VT_R4) {
                temp_furnace_actual_temp = value.fltVal;
                success_count++;
            }
        }
    } catch (...) {
        debug_printf("DCS OPC设备 '%s' 读取炉膛实际温度时发生异常\n", dcs_name.c_str());
    }
    cleanup_variant();

    // 读取实际炉膛压力（Pa）- 使用压力表报文解析逻辑
    try {
        std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
        if (read_opc_item(opc_actual_furnace_pressure_tag, value) == 0) {
            if (value.vt == VT_R4) {
                float raw_pressure_value = value.fltVal;

                // 检查是否为压力表的原始16位整数值（需要进行补码转换）
                if (raw_pressure_value >= 0.0f && raw_pressure_value <= 65535.0f &&
                    raw_pressure_value == (float)(int)raw_pressure_value) {
                    // 处理16位整数值的情况，使用专用解析函数
                    uint16_t raw_uint16 = (uint16_t)raw_pressure_value;
                    temp_actual_furnace_pressure = parse_pressure_data(raw_uint16);

                    debug_printf("DCS OPC压力表数据解析: 原始值=%.0f, 使用压力表解析函数, 最终压力值=%.1f Pa\n",
                               raw_pressure_value, temp_actual_furnace_pressure);
                } else {
                    // 如果已经是处理过的浮点数值，直接使用
                    temp_actual_furnace_pressure = raw_pressure_value;
                    debug_printf("DCS OPC压力表数据: 直接使用浮点值=%.1f Pa\n", temp_actual_furnace_pressure);
                }

                success_count++;
            }
        }
    } catch (...) {
        debug_printf("DCS OPC设备 '%s' 读取实际炉膛压力时发生异常\n", dcs_name.c_str());
    }
    cleanup_variant();

    // 读取给煤量（t/h）
    try {
        std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
        if (read_opc_item(opc_coal_feed_rate_tag, value) == 0) {
            if (value.vt == VT_R4) {
                temp_coal_feed_rate = value.fltVal;
                success_count++;
            }
        }
    } catch (...) {
        debug_printf("DCS OPC设备 '%s' 读取给煤量时发生异常\n", dcs_name.c_str());
    }
    cleanup_variant();

    // 读取实际生料量1（t/h）
    try {
        std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
        if (read_opc_item(opc_actual_raw_material1_tag, value) == 0) {
            if (value.vt == VT_R4) {
                temp_actual_raw_material1 = value.fltVal;
                success_count++;
            }
        }
    } catch (...) {
        debug_printf("DCS OPC设备 '%s' 读取实际生料量1时发生异常\n", dcs_name.c_str());
    }
    cleanup_variant();

    // 读取实际生料量2（t/h）
    try {
        std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
        if (read_opc_item(opc_actual_raw_material2_tag, value) == 0) {
            if (value.vt == VT_R4) {
                temp_actual_raw_material2 = value.fltVal;
                success_count++;
            }
        }
    } catch (...) {
        debug_printf("DCS OPC设备 '%s' 读取实际生料量2时发生异常\n", dcs_name.c_str());
    }
    cleanup_variant();

    // 读取计划生料量（t/h）- 检查标签是否为空（DCS待新建）
    if (!opc_planned_raw_material_tag.empty()) {
        try {
            std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
            if (read_opc_item(opc_planned_raw_material_tag, value) == 0) {
                if (value.vt == VT_R4) {
                    temp_planned_raw_material = value.fltVal;
                    success_count++;
                }
            }
        } catch (...) {
            debug_printf("DCS OPC设备 '%s' 读取计划生料量时发生异常\n", dcs_name.c_str());
        }
        cleanup_variant();
    }

    // 读取引风机转速（rpm）
    try {
        std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
        if (read_opc_item(opc_induced_draft_fan_speed_tag, value) == 0) {
            if (value.vt == VT_R4) {
                temp_induced_draft_fan_speed = value.fltVal;
                success_count++;
            }
        }
    } catch (...) {
        debug_printf("DCS OPC设备 '%s' 读取引风机转速时发生异常\n", dcs_name.c_str());
    }
    cleanup_variant();

    // 读取给煤量设定（t/h）
    if (!opc_coal_feed_set_tag.empty()) {
        try {
            std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
            if (read_opc_item(opc_coal_feed_set_tag, value) == 0) {
                if (value.vt == VT_R4) {
                    temp_coal_feed_set = value.fltVal;
                    success_count++;
                }
            }
        } catch (...) {
            debug_printf("DCS OPC设备 '%s' 读取给煤量设定时发生异常\n", dcs_name.c_str());
        }
        cleanup_variant();
    }

    // 读取生料量1设定（t/h）
    if (!opc_raw_material1_set_tag.empty()) {
        try {
            std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
            if (read_opc_item(opc_raw_material1_set_tag, value) == 0) {
                if (value.vt == VT_R4) {
                    temp_raw_material1_set = value.fltVal;
                    success_count++;
                }
            }
        } catch (...) {
            debug_printf("DCS OPC设备 '%s' 读取生料量1设定时发生异常\n", dcs_name.c_str());
        }
        cleanup_variant();
    }

    // 读取生料量2设定（t/h）
    if (!opc_raw_material2_set_tag.empty()) {
        try {
            std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
            if (read_opc_item(opc_raw_material2_set_tag, value) == 0) {
                if (value.vt == VT_R4) {
                    temp_raw_material2_set = value.fltVal;
                    success_count++;
                }
            }
        } catch (...) {
            debug_printf("DCS OPC设备 '%s' 读取生料量2设定时发生异常\n", dcs_name.c_str());
        }
        cleanup_variant();
    }

    // 读取引风机转速设定（rpm）
    if (!opc_induced_draft_fan_set_tag.empty()) {
        try {
            std::this_thread::sleep_for(std::chrono::milliseconds(READ_DELAY_MS));
            if (read_opc_item(opc_induced_draft_fan_set_tag, value) == 0) {
                if (value.vt == VT_R4) {
                    temp_induced_draft_fan_set = value.fltVal;
                    success_count++;
                }
            }
        } catch (...) {
            debug_printf("DCS OPC设备 '%s' 读取引风机转速设定时发生异常\n", dcs_name.c_str());
        }
        cleanup_variant();
    }

    // 最终清理VARIANT资源
    VariantClear(&value);

    // 原子性更新所有成员变量（使用互斥锁保证线程安全）
    // 这样可以确保其他线程读取数据时获得一致的数据集
    {
        std::lock_guard<std::mutex> lock(rwMutex);
        furnace_set_temp = temp_furnace_set_temp;
        furnace_actual_temp = temp_furnace_actual_temp;
        actual_furnace_pressure = temp_actual_furnace_pressure;
        coal_feed_rate = temp_coal_feed_rate;
        actual_raw_material1 = temp_actual_raw_material1;
        actual_raw_material2 = temp_actual_raw_material2;
        planned_raw_material = temp_planned_raw_material;
        induced_draft_fan_speed = temp_induced_draft_fan_speed;

        // 更新新增的四个设定值
        coal_feed_set = temp_coal_feed_set;
        raw_material1_set = temp_raw_material1_set;
        raw_material2_set = temp_raw_material2_set;
        induced_draft_fan_set = temp_induced_draft_fan_set;
    }

    // 输出调试信息，显示读取的数据
    debug_printf("DCS OPC设备 '%s' 数据读取完成，成功读取 %d 个参数，连接状态: %s\n",
               dcs_name.c_str(), success_count, opc_connected ? "已连接" : "未连接");
    debug_printf("DCS OPC分解炉参数: 炉膛设定温度=%.1f, 炉膛实际温度=%.1f, 实际炉压=%.1f, 给煤量=%.3f\n",
               temp_furnace_set_temp, temp_furnace_actual_temp, temp_actual_furnace_pressure, temp_coal_feed_rate);
    debug_printf("DCS OPC生料量参数: 实际生料量1=%.3f, 实际生料量2=%.3f, 计划生料量=%.3f\n",
               temp_actual_raw_material1, temp_actual_raw_material2, temp_planned_raw_material);
    debug_printf("DCS OPC风机参数: 引风机转速=%.1f\n",
               temp_induced_draft_fan_speed);
    debug_printf("DCS OPC设定值参数: 给煤量设定=%.3f, 生料量1设定=%.3f, 生料量2设定=%.3f, 引风机转速设定=%.1f\n",
               temp_coal_feed_set, temp_raw_material1_set, temp_raw_material2_set, temp_induced_draft_fan_set);

    // 关键修复：只要OPC连接正常，就返回成功，即使某些标签读取失败
    // 这避免了因为空标签配置导致的无限重连问题
    if (opc_connected && (opc_server_handle != 0) && (opc_group_handle != 0)) {
        debug_printf("DCS OPC设备 '%s' OPC连接正常，成功读取 %d 个参数\n",
                   dcs_name.c_str(), success_count);
        return success_count > 0 ? success_count : 1;  // 至少返回1表示连接正常
    } else {
        debug_printf("DCS OPC设备 '%s' OPC连接异常，返回失败\n", dcs_name.c_str());
        return 0;
    }
#else
    debug_printf("DCS OPC设备 '%s' OPC功能仅在Windows平台支持\n", dcs_name.c_str());
    return 0;
#endif
}

/**
 * @brief 统一数据采集线程函数（32位程序稳定性优化）
 * @details 单线程模式，循环执行以下操作：
 *          1. 从OPC服务器读取所有参数（炉膛温度、压力、煤量、生料量等）
 *          2. 将数据保存到CSV文件
 *          3. 更新内存中的数据供其他模块使用
 *          4. 执行参数调整检查
 *          5. 按配置的采集间隔等待下次采集
 */
void DCSOPCDevice::do_data_collect() {
    debug_printf("DCS OPC设备 %s 统一采集线程启动（32位程序优化：单线程模式）\n", this->dcs_name.c_str());

    // 初始化CSV文件管理器，用于数据记录
    csv_manager_t csv;

    // 添加异常处理保护整个线程
    try {

    // 检查关联锅炉配置 - CSV文件以锅炉名称命名
    if (this->associated_boiler.empty()) {
        debug_printf("错误: DCS OPC设备 '%s' 没有关联的锅炉名称，无法创建CSV文件\n", this->dcs_name.c_str());
        return;
    }

    debug_printf("DCS OPC设备 '%s' 关联的锅炉: '%s'\n", this->dcs_name.c_str(), this->associated_boiler.c_str());

    // 使用锅炉名称作为CSV文件前缀
    init_csv_manager(&csv, 0, 4096, 5, this->associated_boiler.c_str());



    // 主要参数采集循环
    // 持续采集DCS数据并记录到CSV文件
    int count = 0;          // 总采集次数计数
    int success_count = 0;  // 成功采集次数计数

    while (true) {
        count++;

        // 执行数据采集操作
        int result = this->read_data();
        bool has_dcs_data = (result > 0);  // 检查是否成功获取DCS数据

        // 定义本次循环使用的DCS数据变量
        float furnace_actual_temp = 0.0f, actual_furnace_pressure = 0.0f;
        float coal_feed_rate = 0.0f;
        float actual_raw_material1 = 0.0f, actual_raw_material2 = 0.0f;

        // 新增四个设定值变量
        float coal_feed_set = 0.0f, raw_material1_set = 0.0f;
        float raw_material2_set = 0.0f, induced_draft_fan_set = 0.0f;

        if (has_dcs_data) {
            success_count++;
            // 从成员变量复制数据到本地变量（使用互斥锁保护）
            // 这样可以减少锁的持有时间，提高并发性能
            {
                std::lock_guard<std::mutex> lock(this->rwMutex);
                furnace_actual_temp = this->furnace_actual_temp;
                actual_furnace_pressure = this->actual_furnace_pressure;
                coal_feed_rate = this->coal_feed_rate;

                // 32位程序优化：生料量数据现在由同一线程采集，直接使用
                actual_raw_material1 = this->actual_raw_material1;
                actual_raw_material2 = this->actual_raw_material2;

                // 获取新增的四个设定值
                coal_feed_set = this->coal_feed_set;
                raw_material1_set = this->raw_material1_set;
                raw_material2_set = this->raw_material2_set;
                induced_draft_fan_set = this->induced_draft_fan_set;
            }
        }

        // 获取关联锅炉的烟气分析仪数据
        // 注意：烟气分析仪数据仅用于CSV记录，不会发送给DCS系统
        // 这样可以保持完整的数据记录，便于后续分析和审计
        float boiler_co = 0.0f, boiler_o2 = 0.0f;
        float boiler_current = 0.0f, boiler_voltage = 0.0f, boiler_temperature = 0.0f;

        // 从全局锅炉映射中查找关联的锅炉设备
        extern std::unordered_map<std::string, Boiler*> boiler_map;
        auto boiler_it = boiler_map.find(this->associated_boiler);
        if (boiler_it != boiler_map.end() && boiler_it->second) {
            Boiler* boiler = boiler_it->second;

            // 使用锅炉的互斥锁保护数据读取，确保数据一致性
            std::lock_guard<std::mutex> boiler_lock(boiler->rwMutex);
            boiler_co = boiler->co;              // 一氧化碳浓度 (ppm)
            boiler_o2 = boiler->o2;              // 氧气浓度 (%)
            boiler_current = boiler->current;    // 电流 (A)
            boiler_voltage = boiler->voltage;    // 电压 (V)
            boiler_temperature = boiler->temperature; // 温度 (°C)

            // 定期输出调试信息（每20次采集输出一次，避免日志过多）
            if (count % 20 == 0) {
                debug_printf("DCS OPC获取到烟气分析仪数据: CO=%.2f, O2=%.2f\n",
                            boiler_co, boiler_o2);
            }
        } else {
            // 无法找到关联锅炉时的调试信息
            if (count % 20 == 0) {
                debug_printf("DCS OPC无法获取烟气分析仪数据: 关联锅炉='%s', 锅炉映射大小=%zu\n",
                            this->associated_boiler.c_str(), boiler_map.size());
            }
        }

        // 检查是否有任何有效数据需要记录
        // 修复：放宽数据有效性判断条件，避免因为数值为0而不写入CSV
        // 烟气分析仪数据：只要不是明显的无效值就认为有效
        bool has_boiler_data = (boiler_co >= 0 || boiler_o2 >= 0 ||
                               boiler_temperature > -999 || boiler_voltage >= 0 || boiler_current >= 0);

        // 只要烟气分析仪或DCS任一有数据，就进行CSV记录
        bool has_any_data = has_boiler_data || has_dcs_data;

        // 增加强制写入机制：即使没有有效数据，每60次采集也强制写入一次（用于调试和确保CSV文件创建）
        static int force_write_counter = 0;
        force_write_counter++;
        if (force_write_counter >= 60) {  // 每60次采集强制写入一次
            has_any_data = true;
            force_write_counter = 0;
            debug_printf("强制写入CSV：第%d次采集，确保CSV文件正常创建\n", count);
        }

        // 将数据写入CSV文件进行持久化存储
        if (has_any_data) {
            // 格式化合并数据行 - 烟气分析仪数据(5个) + DCS数据(6个) + DCS设定值数据(4个) = 15个字段
            // CSV格式：O2,CO,测点温度,电压,电流,炉膛实际温度,实际炉压,给煤量,实际生料量1,实际生料量2,引风机转速,给煤量设定,生料量1设定,生料量2设定,引风机转速设定
            // 注意：DCS数据单位已经是t/h，直接使用无需转换
            char combined_data_line[1024];
            snprintf(combined_data_line, sizeof(combined_data_line),
                    "%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.3f,%.3f,%.3f,%.2f,%.3f,%.3f,%.3f,%.2f",
                    boiler_o2, boiler_co, boiler_temperature, boiler_voltage, boiler_current,
                    furnace_actual_temp, actual_furnace_pressure,
                    coal_feed_rate, actual_raw_material1, actual_raw_material2, induced_draft_fan_speed,
                    coal_feed_set, raw_material1_set, raw_material2_set, induced_draft_fan_set);

            // 将格式化的数据写入CSV缓冲区
            // CSV管理器会自动处理文件创建、时间戳添加和缓冲区管理
            int csv_result = write_to_csv_buffer(&csv, (const unsigned char*)combined_data_line, strlen(combined_data_line));

            // 输出数据记录状态信息
            if (csv_result == 0) {
                // CSV写入成功
                if (has_dcs_data) {
                    debug_printf("DCS OPC+仪器分析仪数据已写入CSV: DCS数据=有效, 仪器O2=%.2f, CO=%.2f\n", boiler_o2, boiler_co);
                } else {
                    debug_printf("仅仪器分析仪数据已写入CSV: O2=%.2f, CO=%.2f\n",
                                boiler_o2, boiler_co);
                }
            } else {
                // CSV写入失败，但程序继续运行
                debug_printf("CSV写入失败(返回值=%d)，但数据采集继续正常运行\n", csv_result);
            }
        } else {
            // 没有有效数据时的调试信息（每20次输出一次）
            if (count % 20 == 0) {
                debug_printf("第%d次采集：无有效数据，跳过CSV写入 - 烟气数据有效=%s, DCS数据有效=%s\n",
                            count, has_boiler_data ? "是" : "否", has_dcs_data ? "是" : "否");
                debug_printf("调试: 烟气数据值 - O2=%.2f, CO=%.2f, 温度=%.2f, 电压=%.2f, 电流=%.2f\n",
                            boiler_o2, boiler_co, boiler_temperature, boiler_voltage, boiler_current);
            }
        }

        // 定期输出采集统计信息和健康检查（每10次采集输出一次）
        if (count % 10 == 0) {
            debug_printf("DCS OPC设备 %s 请求次数: %d, 成功响应: %d 次, 分解炉数据: 炉膛实际温度=%.1f, 实际炉压=%.1f, 给煤量=%.3f, 实际生料量1=%.3f, 实际生料量2=%.3f\n",
                        this->dcs_name.c_str(), count, success_count, furnace_actual_temp, actual_furnace_pressure, coal_feed_rate, actual_raw_material1, actual_raw_material2);

            // 健康检查：如果连续多次采集失败，尝试重连
            if (count > 50 && success_count < count / 10) {  // 成功率低于10%
                debug_printf("DCS OPC设备 %s 健康检查: 成功率过低 (%.1f%%)，可能需要重连\n",
                           this->dcs_name.c_str(), (float)success_count * 100.0f / count);
            }
        }

        // 定期内存清理和连接健康检查（每10次采集执行一次）- 32位程序内存管理优化
        // DCS数据主要用于datascreen.cpp和parameter_adjustment.cpp，不需要图表绘制，可以更频繁清理
        if (count % 10 == 0) {
            debug_printf("DCS OPC设备 %s 执行内存清理和连接检查，采集次数: %d\n", this->dcs_name.c_str(), count);

            // 连接健康检查：验证OPC连接的真实状态
            if (opc_connected) {
                // 检查句柄有效性
                if (opc_server_handle == 0 || opc_group_handle == 0) {
                    debug_printf("DCS OPC设备 %s 连接健康检查失败：句柄无效 (server=%lu, group=%lu)\n",
                               this->dcs_name.c_str(), opc_server_handle, opc_group_handle);
                    opc_connected = false;
                }

                // 32位程序稳定性：检查DACLTSDK函数指针有效性
                if (!ASDAC_ReadItem || !ASDAC_WriteItem || !ASDAC_GetServerStatus) {
                    debug_printf("DCS OPC设备 %s 连接健康检查失败：DACLTSDK函数指针无效\n", this->dcs_name.c_str());
                    opc_connected = false;
                }
            }

            try {
                // 32位程序内存优化：更积极的COM库清理
                if (com_initialized) {
                    CoFreeUnusedLibraries();
                    // 32位程序额外清理：强制垃圾回收（修复参数类型）
                    CoFreeUnusedLibrariesEx(0, 0);  // 第一个参数是延迟时间(DWORD)，不是COLE_DEFAULT_PRINCIPAL
                }

                // 刷新CSV缓冲区，避免缓冲区过大
                // DCS数据量相对较小，使用更小的缓冲区阈值
                if (csv.buffer_size > 1024) {  // 如果缓冲区超过1KB
                    flush_csv_buffer(&csv);
                    debug_printf("DCS OPC设备 %s CSV缓冲区已刷新\n", this->dcs_name.c_str());
                }

                // 检查内存使用情况（Windows特定）- 32位程序内存监控
                #ifdef _WIN32
                PROCESS_MEMORY_COUNTERS pmc;
                if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc))) {
                    SIZE_T workingSetMB = pmc.WorkingSetSize / (1024 * 1024);
                    SIZE_T privateBytesMB = pmc.PagefileUsage / (1024 * 1024);

                    // 32位程序内存警戒线：工作集200MB，私有字节300MB
                    if (workingSetMB > 200) {
                        debug_printf("DCS OPC设备 %s 内存使用警告: 工作集 %zu MB, 私有字节 %zu MB\n",
                                   this->dcs_name.c_str(), workingSetMB, privateBytesMB);
                    }

                    // 32位程序严重内存警告：接近1.5GB限制
                    if (privateBytesMB > 1200) {
                        debug_printf("DCS OPC设备 %s 严重内存警告: 私有字节 %zu MB，接近32位程序限制\n",
                                   this->dcs_name.c_str(), privateBytesMB);
                    }
                }
                #endif

            } catch (...) {
                debug_printf("DCS OPC设备 %s 内存清理时发生异常\n", this->dcs_name.c_str());
            }
        }

        // 32位程序优化：在统一采集线程中执行参数调整检查
        if (has_dcs_data && g_parameter_adjustment_manager) {
            try {
                // 更新生料量数据状态
                {
                    std::lock_guard<std::mutex> lock(this->rwMutex);
                    last_raw_material_update = std::chrono::steady_clock::now();
                    raw_material_data_valid = true;
                }

                // 执行参数调整检查（使用实际生料量1+实际生料量2作为总生料量）
                float total_actual_raw_material = actual_raw_material1 + actual_raw_material2;
                g_parameter_adjustment_manager->check_and_adjust_parameters(
                    this->dcs_name, total_actual_raw_material, 0.0f);  // 暂时不使用计划生料量

                debug_printf("DCS OPC统一采集: 生料量数据更新并执行参数调整检查 - 实际生料量1=%.4f, 实际生料量2=%.4f, 总生料量=%.4f\n",
                           actual_raw_material1, actual_raw_material2, total_actual_raw_material);
            } catch (...) {
                debug_printf("DCS OPC设备 %s 参数调整检查时发生异常\n", this->dcs_name.c_str());
            }
        }



        // 按照配置的采集间隔等待下次采集
        // 使用sleep_for确保采集频率的稳定性
        std::this_thread::sleep_for(std::chrono::seconds(this->collection_interval));
    }

        // 线程退出时的清理工作
        close_csv_manager(&csv);  // 关闭CSV文件管理器，确保数据完整写入
        debug_printf("DCS OPC设备 %s 统一采集线程退出\n", this->dcs_name.c_str());

    } catch (const std::exception& e) {
        debug_printf("DCS OPC设备 %s 统一采集线程异常: %s\n", this->dcs_name.c_str(), e.what());
        close_csv_manager(&csv);  // 确保异常时也关闭CSV文件
    } catch (...) {
        debug_printf("DCS OPC设备 %s 统一采集线程发生未知异常\n", this->dcs_name.c_str());
        close_csv_manager(&csv);  // 确保异常时也关闭CSV文件
    }
}

/**
 * @brief 生料量独立采集线程函数（32位程序稳定性优化：已废弃）
 * @details 为了提高32位程序稳定性，生料量数据现在由主采集线程统一采集
 *          此函数保留接口兼容性，但不再执行实际采集操作
 */
void DCSOPCDevice::do_raw_material_collect() {
    debug_printf("DCS OPC设备 %s 32位程序优化：生料量采集已合并到统一采集线程，此独立线程不再执行采集操作\n",
                this->dcs_name.c_str());

    // 不再执行任何采集操作，生料量数据由do_data_collect统一处理
    // 保留此函数仅为接口兼容性，实际不会被调用
}

/**
 * @brief 获取并初始化所有DCS OPC设备
 * @param config_manager 配置管理器指针
 * @return 包含所有DCS OPC设备的映射表
 * @details 此函数负责：
 *          1. 从config.ini读取DCS设备列表
 *          2. 为每个DCS设备创建DCSOPCDevice实例
 *          3. 连接到相应的OPC服务器
 *          4. 返回设备映射表供其他模块使用
 */
std::unordered_map<std::string, DCSOPCDevice*> get_dcs_opc_list(ConfigManager *config_manager) {
    // 从配置文件读取DCS设备列表（如："DCS1,DCS2"）
    std::string dcs_list = config_manager->get<std::string>("DCSList", "list");
    debug_printf("调试: 从配置读取的DCS设备列表: '%s'\n", dcs_list.c_str());

    std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_device_map;
    std::vector<std::string> vect_dcs = stringSplit(dcs_list, ','); // 按逗号分割设备名称

    debug_printf("调试: 解析后的DCS设备数量: %zu\n", vect_dcs.size());

    for (const auto& dcs_name : vect_dcs) {
        // 检查该DCS设备是否配置为使用OPC通信
        std::string communication_type = config_manager->get<std::string>(dcs_name, "CommunicationType", "Modbus");

        // 检查该DCS设备是否配置为使用OPC通信
        if (communication_type == "OPC") {
            debug_printf("调试: 创建DCS OPC设备: '%s'\n", dcs_name.c_str());

            // 创建新的DCS OPC设备实例
            // 构造函数会自动读取配置并尝试连接OPC服务器
            DCSOPCDevice* dcs_opc_device = new DCSOPCDevice(config_manager, dcs_name);

            // 检查DCS OPC设备是否成功初始化
            // 初始化包括配置读取、OPC服务器连接、OPC组和项的创建
            if (dcs_opc_device->is_initialized) {
                dcs_opc_device_map[dcs_name] = dcs_opc_device;
                debug_printf("调试: DCS OPC设备 '%s' 创建并初始化成功\n", dcs_name.c_str());
            } else {
                debug_printf("错误: DCS OPC设备 '%s' 初始化失败，跳过该设备\n", dcs_name.c_str());
                delete dcs_opc_device;  // 释放失败的设备对象
            }
        } else {
            // 该设备使用其他通信方式（如Modbus），跳过OPC设备创建
            debug_printf("调试: DCS设备 '%s' 使用 %s 通信，跳过OPC设备创建\n", dcs_name.c_str(), communication_type.c_str());
        }
    }

    debug_printf("调试: 总共创建了 %zu 个DCS OPC设备\n", dcs_opc_device_map.size());
    return dcs_opc_device_map;
}

/**
 * @brief 获取指定DCS设备的实时数据
 * @param dcs_name DCS设备名称（如"DCS1"）
 * @param furnace_set_temp 输出参数：炉膛设定温度
 * @param furnace_actual_temp 输出参数：炉膛实际温度
 * @param actual_furnace_pressure 输出参数：实际炉压
 * @param coal_feed_rate 输出参数：给煤量
 * @param actual_raw_material 输出参数：实际生料量
 * @param planned_raw_material 输出参数：计划生料量

 * @param induced_draft_fan_speed 输出参数：引风机转速
 * @details 此函数供其他模块调用，用于获取DCS的实时数据
 *          数据来源于OPC采集线程更新的内存数据
 */
void get_realtime_dcs_opc_data(std::string dcs_name,
                              float *furnace_set_temp,
                              float *furnace_actual_temp,
                              float *actual_furnace_pressure,
                              float *coal_feed_rate,
                              float *actual_raw_material,
                              float *planned_raw_material,
                              float *induced_draft_fan_speed) {
    // 记录数据请求的调试信息
    debug_printf("调试: 请求DCS OPC设备名称: %s\n", dcs_name.c_str());
    debug_printf("调试: dcs_opc_map大小: %zu\n", dcs_opc_map.size());

    // 检查全局设备映射表是否为空
    if (dcs_opc_map.empty()) {
        debug_printf("错误: dcs_opc_map为空！可能是OPC设备尚未初始化\n");
        return;
    }

    // 在全局设备映射表中查找指定的DCS OPC设备
    auto it = dcs_opc_map.find(dcs_name);
    if (it == dcs_opc_map.end()) {
        debug_printf("错误: 找不到DCS OPC设备 '%s'\n", dcs_name.c_str());
        // 设置所有输出参数为默认值（0.0）
        *furnace_set_temp = *furnace_actual_temp = *actual_furnace_pressure = 0.0f;
        *coal_feed_rate = *actual_raw_material = *planned_raw_material = 0.0f;
        *induced_draft_fan_speed = 0.0f;
        return;
    }

    // 获取DCS OPC设备对象指针
    DCSOPCDevice* dcs_opc_device = it->second;
    if (!dcs_opc_device) {
        debug_printf("错误: DCS OPC设备 '%s' 对象为空\n", dcs_name.c_str());
        // 设置所有输出参数为默认值
        *furnace_set_temp = *furnace_actual_temp = *actual_furnace_pressure = 0.0f;
        *coal_feed_rate = *actual_raw_material = *planned_raw_material = 0.0f;
        *induced_draft_fan_speed = 0.0f;
        return;
    }

    // 使用互斥锁保护数据读取，确保数据一致性
    // 这样可以避免在数据更新过程中读取到不一致的数据
    {
        std::lock_guard<std::mutex> lock(dcs_opc_device->rwMutex);

        // 复制所有DCS参数到输出参数（兼容性接口：使用实际生料量1作为主要生料量）
        *furnace_set_temp = dcs_opc_device->furnace_set_temp;                   // 炉膛设定温度 (°C)
        *furnace_actual_temp = dcs_opc_device->furnace_actual_temp;             // 炉膛实际温度 (°C)
        *actual_furnace_pressure = dcs_opc_device->actual_furnace_pressure;     // 实际炉膛压力 (Pa)
        *coal_feed_rate = dcs_opc_device->coal_feed_rate;                       // 给煤量 (t/h)
        *actual_raw_material = dcs_opc_device->actual_raw_material1;            // 实际生料量1 (t/h)
        *planned_raw_material = dcs_opc_device->planned_raw_material;           // 计划生料量 (t/h)
        *induced_draft_fan_speed = dcs_opc_device->induced_draft_fan_speed; // 引风机转速 (rpm)
    }

    // 输出获取到的数据用于调试
    debug_printf("调试: DCS OPC设备 '%s' 分解炉数据获取成功 - 炉膛设定温度=%.1f, 炉膛实际温度=%.1f, 实际炉压=%.1f, 给煤量=%.3f, 实际生料量=%.3f, 计划生料量=%.3f\n",
                dcs_name.c_str(), *furnace_set_temp, *furnace_actual_temp, *actual_furnace_pressure, *coal_feed_rate, *actual_raw_material, *planned_raw_material);
    debug_printf("调试: DCS OPC设备 '%s' 风机数据获取成功 - 引风机转速=%.1f\n",
                dcs_name.c_str(), *induced_draft_fan_speed);
}

/**
 * @brief 获取指定DCS设备的实时数据（新版本，支持双生料量）
 * @param dcs_name DCS设备名称（如"DCS1"）
 * @param furnace_actual_temp 输出参数：炉膛实际温度
 * @param actual_furnace_pressure 输出参数：实际炉压
 * @param coal_feed_rate 输出参数：给煤量
 * @param actual_raw_material1 输出参数：实际生料量1
 * @param actual_raw_material2 输出参数：实际生料量2
 * @param induced_draft_fan_speed 输出参数：引风机转速
 * @details 此函数供其他模块调用，用于获取DCS的实时数据
 *          数据来源于OPC采集线程更新的内存数据
 */
void get_realtime_dcs_opc_data_v2(std::string dcs_name,
                                 float *furnace_actual_temp,
                                 float *actual_furnace_pressure,
                                 float *coal_feed_rate,
                                 float *actual_raw_material1,
                                 float *actual_raw_material2,
                                 float *induced_draft_fan_speed) {
    // 记录数据请求的调试信息
    debug_printf("调试: 请求DCS OPC设备名称: %s (v2接口)\n", dcs_name.c_str());

    // 检查全局设备映射表是否为空
    if (dcs_opc_map.empty()) {
        debug_printf("错误: dcs_opc_map为空！可能是OPC设备尚未初始化\n");
        return;
    }

    // 在全局设备映射表中查找指定的DCS OPC设备
    auto it = dcs_opc_map.find(dcs_name);
    if (it == dcs_opc_map.end()) {
        debug_printf("错误: 找不到DCS OPC设备 '%s'\n", dcs_name.c_str());
        // 设置所有输出参数为默认值（0.0）
        *furnace_actual_temp = *actual_furnace_pressure = 0.0f;
        *coal_feed_rate = *actual_raw_material1 = *actual_raw_material2 = 0.0f;
        *induced_draft_fan_speed = 0.0f;
        return;
    }

    // 获取DCS OPC设备对象指针
    DCSOPCDevice* dcs_opc_device = it->second;
    if (!dcs_opc_device) {
        debug_printf("错误: DCS OPC设备 '%s' 对象指针为空\n", dcs_name.c_str());
        return;
    }

    // 使用互斥锁保护数据读取，确保数据一致性
    {
        std::lock_guard<std::mutex> lock(dcs_opc_device->rwMutex);

        // 复制所有DCS参数到输出参数
        *furnace_actual_temp = dcs_opc_device->furnace_actual_temp;             // 炉膛实际温度 (°C)
        *actual_furnace_pressure = dcs_opc_device->actual_furnace_pressure;     // 实际炉膛压力 (Pa)
        *coal_feed_rate = dcs_opc_device->coal_feed_rate;                       // 给煤量 (t/h)
        *actual_raw_material1 = dcs_opc_device->actual_raw_material1;           // 实际生料量1 (t/h)
        *actual_raw_material2 = dcs_opc_device->actual_raw_material2;           // 实际生料量2 (t/h)
        *induced_draft_fan_speed = dcs_opc_device->induced_draft_fan_speed; // 引风机转速 (rpm)
    }

    // 输出获取到的数据用于调试
    debug_printf("调试: DCS OPC设备 '%s' 分解炉数据获取成功 - 炉膛实际温度=%.1f, 实际炉压=%.1f, 给煤量=%.3f, 实际生料量1=%.3f, 实际生料量2=%.3f\n",
                dcs_name.c_str(), *furnace_actual_temp, *actual_furnace_pressure, *coal_feed_rate, *actual_raw_material1, *actual_raw_material2);
    debug_printf("调试: DCS OPC设备 '%s' 风机数据获取成功 - 引风机转速=%.1f\n",
                dcs_name.c_str(), *induced_draft_fan_speed);
}

/**
 * @brief 获取指定DCS设备的设定值数据
 * @param dcs_name DCS设备名称（如"DCS1"）
 * @param coal_feed_set 输出参数：给煤量设定值
 * @param raw_material1_set 输出参数：生料量1设定值
 * @param raw_material2_set 输出参数：生料量2设定值
 * @param induced_draft_fan_set 输出参数：引风机转速设定值
 * @details 此函数供其他模块调用，用于获取DCS的设定值数据
 *          数据来源于OPC采集线程更新的内存数据
 */
void get_realtime_dcs_opc_setpoint_data(std::string dcs_name,
                                       float *coal_feed_set,
                                       float *raw_material1_set,
                                       float *raw_material2_set,
                                       float *induced_draft_fan_set) {
    // 记录数据请求的调试信息
    debug_printf("调试: 请求DCS OPC设备设定值数据，设备名称: %s\n", dcs_name.c_str());

    // 检查全局设备映射表是否为空
    if (dcs_opc_map.empty()) {
        debug_printf("错误: dcs_opc_map为空！可能是OPC设备尚未初始化\n");
        return;
    }

    // 在全局设备映射表中查找指定的DCS OPC设备
    auto it = dcs_opc_map.find(dcs_name);
    if (it == dcs_opc_map.end()) {
        debug_printf("错误: 找不到DCS OPC设备 '%s'\n", dcs_name.c_str());
        // 设置所有输出参数为默认值（0.0）
        *coal_feed_set = *raw_material1_set = *raw_material2_set = 0.0f;
        *induced_draft_fan_set = 0.0f;
        return;
    }

    // 获取DCS OPC设备对象指针
    DCSOPCDevice* dcs_opc_device = it->second;
    if (!dcs_opc_device) {
        debug_printf("错误: DCS OPC设备 '%s' 对象指针为空\n", dcs_name.c_str());
        return;
    }

    // 使用互斥锁保护数据读取，确保数据一致性
    {
        std::lock_guard<std::mutex> lock(dcs_opc_device->rwMutex);

        // 复制所有DCS设定值参数到输出参数
        *coal_feed_set = dcs_opc_device->coal_feed_set;                         // 给煤量设定值 (t/h)
        *raw_material1_set = dcs_opc_device->raw_material1_set;                 // 生料量1设定值 (t/h)
        *raw_material2_set = dcs_opc_device->raw_material2_set;                 // 生料量2设定值 (t/h)
        *induced_draft_fan_set = dcs_opc_device->induced_draft_fan_set;         // 引风机转速设定值 (rpm)
    }

    // 输出获取到的数据用于调试
    debug_printf("调试: DCS OPC设备 '%s' 设定值数据获取成功 - 给煤量设定=%.3f, 生料量1设定=%.3f, 生料量2设定=%.3f, 引风机转速设定=%.1f\n",
                dcs_name.c_str(), *coal_feed_set, *raw_material1_set, *raw_material2_set, *induced_draft_fan_set);
}
