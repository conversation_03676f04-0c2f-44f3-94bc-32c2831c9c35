#include "redisclient.h"
#include <QEventLoop>
#include <QTimer>

RedisClient::RedisClient(QObject *parent)
    : QObject(parent)
    , m_socket(nullptr)
    , m_host("127.0.0.1")
    , m_port(6379)
    , m_database(0)
    , m_connected(false)
    , m_defaultExpireSeconds(86400) // 24小时
    , m_connectionTimer(nullptr)
    , m_cleanupTimer(nullptr)
    , m_waitingForResponse(false)
    , m_reconnectAttempts(0)
{
    m_socket = new QTcpSocket(this);
    
    connect(m_socket, &QTcpSocket::connected, this, &RedisClient::onSocketConnected);
    connect(m_socket, &QTcpSocket::disconnected, this, &RedisClient::onSocketDisconnected);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
            this, &RedisClient::onSocketError);
    connect(m_socket, &QTcpSocket::readyRead, this, &RedisClient::onSocketReadyRead);
    
    setupConnectionTimer();
}

RedisClient::~RedisClient()
{
    disconnectFromRedis();
}

bool RedisClient::connectToRedis(const QString &host, int port, int database, const QString &password)
{
    if (m_connected) {
        disconnectFromRedis();
    }

    m_host = host;
    m_port = port;
    m_database = database;
    m_password = password;

    qDebug() << "Redis: 尝试连接到" << host << ":" << port << "数据库" << database;

    m_socket->connectToHost(host, port);

    // 等待连接完成
    if (m_socket->waitForConnected(5000)) {
        // 如果有密码，先进行认证
        if (!password.isEmpty()) {
            if (!authenticatePassword(password)) {
                qDebug() << "Redis: 密码认证失败";
                disconnectFromRedis();
                return false;
            }
        }

        // 选择数据库
        if (database != 0) {
            return selectDatabase(database);
        }
        return true;
    }

    qDebug() << "Redis: 连接失败:" << m_socket->errorString();
    return false;
}

void RedisClient::disconnectFromRedis()
{
    if (m_socket && m_socket->state() == QAbstractSocket::ConnectedState) {
        m_socket->disconnectFromHost();
        if (m_socket->state() != QAbstractSocket::UnconnectedState) {
            m_socket->waitForDisconnected(3000);
        }
    }
    m_connected = false;
}

bool RedisClient::isConnected() const
{
    return m_connected && m_socket && m_socket->state() == QAbstractSocket::ConnectedState;
}

bool RedisClient::setChartData(const QString &key, const QVariantList &data, int expireSeconds)
{
    if (!isConnected()) {
        qDebug() << "Redis: 未连接，无法设置数据";
        return false;
    }
    
    QByteArray serializedData = serializeVariantList(data);
    if (serializedData.isEmpty()) {
        qDebug() << "Redis: 数据序列化失败";
        return false;
    }
    
    // 使用SETEX命令设置带过期时间的数据
    QStringList args;
    args << "SETEX" << key << QString::number(expireSeconds) << QString::fromUtf8(serializedData);
    
    QVariant response = sendCommandAndWait(args);
    bool success = (response.toString() == "OK");
    
    if (success) {
        qDebug() << "Redis: 成功设置缓存数据，键:" << key << "数据点数:" << data.size() << "过期时间:" << expireSeconds << "秒";
    } else {
        qDebug() << "Redis: 设置数据失败，键:" << key << "响应:" << response.toString();
    }
    
    return success;
}

QVariantList RedisClient::getChartData(const QString &key)
{
    if (!isConnected()) {
        qDebug() << "Redis: 未连接，无法获取数据";
        return QVariantList();
    }
    
    QStringList args;
    args << "GET" << key;
    
    QVariant response = sendCommandAndWait(args);
    
    if (response.isNull() || response.toString().isEmpty()) {
        qDebug() << "Redis: 键不存在或已过期:" << key;
        return QVariantList();
    }
    
    QByteArray data = response.toString().toUtf8();
    QVariantList result = deserializeVariantList(data);
    
    qDebug() << "Redis: 成功获取缓存数据，键:" << key << "数据点数:" << result.size();
    return result;
}

bool RedisClient::hasChartData(const QString &key)
{
    if (!isConnected()) {
        return false;
    }
    
    QStringList args;
    args << "EXISTS" << key;
    
    QVariant response = sendCommandAndWait(args);
    return response.toInt() == 1;
}

bool RedisClient::deleteKey(const QString &key)
{
    if (!isConnected()) {
        return false;
    }
    
    QStringList args;
    args << "DEL" << key;
    
    QVariant response = sendCommandAndWait(args);
    return response.toInt() > 0;
}

QStringList RedisClient::getKeys(const QString &pattern)
{
    if (!isConnected()) {
        return QStringList();
    }
    
    QStringList args;
    args << "KEYS" << pattern;
    
    QVariant response = sendCommandAndWait(args);
    
    // Redis返回的是数组格式，需要解析
    if (response.type() == QVariant::StringList) {
        return response.toStringList();
    }
    
    return QStringList();
}

bool RedisClient::deleteKeys(const QStringList &keys)
{
    if (!isConnected() || keys.isEmpty()) {
        return false;
    }
    
    QStringList args;
    args << "DEL";
    args.append(keys);
    
    QVariant response = sendCommandAndWait(args);
    return response.toInt() > 0;
}

void RedisClient::clearExpiredKeys()
{
    // Redis会自动清理过期键，这里可以实现自定义清理逻辑
    qDebug() << "Redis: 执行过期键清理检查";
    
    // 获取所有图表数据键
    QStringList keys = getKeys("chart_data_*");
    qDebug() << "Redis: 找到" << keys.size() << "个图表数据键";
}

qint64 RedisClient::getMemoryUsage()
{
    if (!isConnected()) {
        return -1;
    }
    
    QStringList args;
    args << "MEMORY" << "USAGE" << "chart_data_*";
    
    QVariant response = sendCommandAndWait(args);
    return response.toLongLong();
}

int RedisClient::getKeyCount()
{
    QStringList keys = getKeys("chart_data_*");
    return keys.size();
}

void RedisClient::onSocketConnected()
{
    qDebug() << "Redis: Socket连接成功";
    m_connected = true;
    m_reconnectAttempts = 0;
    
    // 如果有密码，先进行认证
    if (!m_password.isEmpty()) {
        if (!authenticatePassword(m_password)) {
            qDebug() << "Redis: 重连时密码认证失败";
            m_connected = false;
            return;
        }
    }

    // 选择数据库
    if (m_database != 0) {
        selectDatabase(m_database);
    }

    emit connected();
}

void RedisClient::onSocketDisconnected()
{
    qDebug() << "Redis: Socket连接断开";
    m_connected = false;
    emit disconnected();
}

void RedisClient::onSocketError(QAbstractSocket::SocketError error)
{
    QString errorMsg = QString("Redis连接错误: %1").arg(m_socket->errorString());
    qDebug() << errorMsg;
    m_connected = false;
    emit this->error(errorMsg);
}

void RedisClient::onSocketReadyRead()
{
    m_responseBuffer.append(m_socket->readAll());
    
    if (m_waitingForResponse) {
        // 简单的响应解析（实际项目中需要更完整的Redis协议解析）
        if (m_responseBuffer.contains("\r\n")) {
            m_lastResponse = parseRedisResponse(m_responseBuffer);
            m_responseBuffer.clear();
            m_waitingForResponse = false;
        }
    }
}

void RedisClient::checkConnection()
{
    if (!isConnected() && m_reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        qDebug() << "Redis: 尝试重新连接，第" << (m_reconnectAttempts + 1) << "次";
        m_reconnectAttempts++;
        connectToRedis(m_host, m_port, m_database, m_password);
    }
}

QString RedisClient::formatRedisCommand(const QStringList &args)
{
    QString command = QString("*%1\r\n").arg(args.size());
    
    for (const QString &arg : args) {
        QByteArray argBytes = arg.toUtf8();
        command += QString("$%1\r\n%2\r\n").arg(argBytes.size()).arg(QString::fromUtf8(argBytes));
    }
    
    return command;
}

QVariant RedisClient::parseRedisResponse(const QByteArray &response)
{
    QString responseStr = QString::fromUtf8(response);
    
    if (responseStr.startsWith("+")) {
        // 简单字符串响应
        return responseStr.mid(1).trimmed();
    } else if (responseStr.startsWith(":")) {
        // 整数响应
        return responseStr.mid(1).trimmed().toInt();
    } else if (responseStr.startsWith("$")) {
        // 批量字符串响应
        QStringList lines = responseStr.split("\r\n");
        if (lines.size() >= 2) {
            int length = lines[0].mid(1).toInt();
            if (length == -1) {
                return QVariant(); // NULL
            }
            return lines[1];
        }
    }
    
    return responseStr.trimmed();
}

bool RedisClient::sendCommand(const QStringList &args)
{
    if (!isConnected()) {
        return false;
    }
    
    QString command = formatRedisCommand(args);
    QByteArray data = command.toUtf8();
    
    qint64 written = m_socket->write(data);
    return written == data.size() && m_socket->waitForBytesWritten(3000);
}

QVariant RedisClient::sendCommandAndWait(const QStringList &args, int timeoutMs)
{
    if (!sendCommand(args)) {
        return QVariant();
    }
    
    m_waitingForResponse = true;
    m_lastResponse = QVariant();
    
    QEventLoop loop;
    QTimer timer;
    timer.setSingleShot(true);
    timer.setInterval(timeoutMs);
    
    connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);
    connect(this, &RedisClient::disconnected, &loop, &QEventLoop::quit);
    
    timer.start();
    
    while (m_waitingForResponse && timer.isActive()) {
        loop.processEvents();
        if (!m_waitingForResponse) {
            break;
        }
    }
    
    m_waitingForResponse = false;
    return m_lastResponse;
}

QByteArray RedisClient::serializeVariantList(const QVariantList &data)
{
    QJsonArray jsonArray;
    
    for (const QVariant &item : data) {
        if (item.type() == QVariant::Map) {
            QVariantMap map = item.toMap();
            QJsonObject jsonObj;
            
            for (auto it = map.begin(); it != map.end(); ++it) {
                jsonObj[it.key()] = QJsonValue::fromVariant(it.value());
            }
            
            jsonArray.append(jsonObj);
        }
    }
    
    QJsonDocument doc(jsonArray);
    return doc.toJson(QJsonDocument::Compact);
}

QVariantList RedisClient::deserializeVariantList(const QByteArray &data)
{
    QVariantList result;
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        qDebug() << "Redis: JSON解析错误:" << error.errorString();
        return result;
    }
    
    if (!doc.isArray()) {
        qDebug() << "Redis: 数据格式错误，期望数组";
        return result;
    }
    
    QJsonArray jsonArray = doc.array();
    for (const QJsonValue &value : jsonArray) {
        if (value.isObject()) {
            QJsonObject jsonObj = value.toObject();
            QVariantMap map;
            
            for (auto it = jsonObj.begin(); it != jsonObj.end(); ++it) {
                map[it.key()] = it.value().toVariant();
            }
            
            result.append(map);
        }
    }
    
    return result;
}

void RedisClient::setupConnectionTimer()
{
    m_connectionTimer = new QTimer(this);
    m_connectionTimer->setInterval(CONNECTION_CHECK_INTERVAL);
    connect(m_connectionTimer, &QTimer::timeout, this, &RedisClient::checkConnection);
    m_connectionTimer->start();
    
    m_cleanupTimer = new QTimer(this);
    m_cleanupTimer->setInterval(CLEANUP_INTERVAL);
    connect(m_cleanupTimer, &QTimer::timeout, this, &RedisClient::clearExpiredKeys);
    m_cleanupTimer->start();
}

bool RedisClient::selectDatabase(int database)
{
    QStringList args;
    args << "SELECT" << QString::number(database);
    
    QVariant response = sendCommandAndWait(args);
    bool success = (response.toString() == "OK");
    
    if (success) {
        qDebug() << "Redis: 成功选择数据库" << database;
    } else {
        qDebug() << "Redis: 选择数据库失败:" << response.toString();
    }
    
    return success;
}

bool RedisClient::authenticatePassword(const QString &password)
{
    if (password.isEmpty()) {
        return true; // 无密码时认为认证成功
    }

    QStringList args;
    args << "AUTH" << password;

    QVariant response = sendCommandAndWait(args);
    bool success = (response.toString() == "OK");

    if (success) {
        qDebug() << "Redis: 密码认证成功";
    } else {
        qDebug() << "Redis: 密码认证失败:" << response.toString();
    }

    return success;
}
