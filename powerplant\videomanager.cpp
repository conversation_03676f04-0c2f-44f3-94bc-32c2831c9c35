#include "videomanager.h"
#include <QDebug>
#include <QFileInfo>
#include <QUrl>

VideoManager::VideoManager(QObject *parent)
    : QObject(parent)
    , m_mediaPlayer(nullptr)
    , m_videoWidget(nullptr)
    , m_isPlaying(false)
    , m_currentBoiler(1)
{
    // 创建媒体播放器
    m_mediaPlayer = new QMediaPlayer(this);

    // 创建视频显示部件
    m_videoWidget = new QVideoWidget();
    m_videoWidget->setMinimumSize(640, 360);
    m_videoWidget->setStyleSheet("background-color: black;");
    m_videoWidget->setWindowTitle("锅炉视频监控");
    m_videoWidget->resize(960, 540);

    // 设置视频输出
    m_mediaPlayer->setVideoOutput(m_videoWidget);

    // 连接信号
    connect(m_mediaPlayer, &QMediaPlayer::playbackStateChanged, this, &VideoManager::onPlaybackStateChanged);
    connect(m_mediaPlayer, &QMediaPlayer::mediaStatusChanged, this, &VideoManager::onMediaStatusChanged);

    // 初始化视频路径列表 - 使用相对路径
    m_videoPaths << "./video/video.mp4"  // 1#锅炉
                 << "./video/video.mp4"  // 2#锅炉
                 << "./video/video.mp4"  // 3#锅炉
                 << "./video/video.mp4"; // 4#锅炉
}

VideoManager::~VideoManager()
{
    if (m_videoWidget) {
        delete m_videoWidget;
    }
}

QWidget* VideoManager::getVideoWidget()
{
    return m_videoWidget;
}

void VideoManager::playVideo(int boilerNumber)
{
    if (!m_mediaPlayer || !m_videoWidget) {
        qWarning() << "Media player not initialized";
        return;
    }

    if (boilerNumber < 1 || boilerNumber > 4) {
        qWarning() << "Invalid boiler number:" << boilerNumber;
        emit videoError("无效的锅炉编号");
        return;
    }

    m_currentBoiler = boilerNumber;
    emit currentBoilerChanged(m_currentBoiler);

    // 获取对应锅炉的视频路径
    QString videoPath = m_videoPaths[boilerNumber - 1];

    // 检查文件是否存在
    QFileInfo fileInfo(videoPath);
    if (!fileInfo.exists()) {
        qWarning() << "Video file not found:" << videoPath;
        emit videoError("视频文件不存在: " + videoPath);
        return;
    }

    // 设置媒体源并播放
    m_mediaPlayer->setSource(QUrl::fromLocalFile(videoPath));
    m_mediaPlayer->play();

    // 显示视频窗口
    m_videoWidget->show();
    m_videoWidget->raise();
    m_videoWidget->activateWindow();

    qDebug() << "Playing video for boiler" << boilerNumber << ":" << videoPath;
}

void VideoManager::pauseVideo()
{
    if (!m_mediaPlayer) {
        return;
    }

    m_mediaPlayer->pause();
    qDebug() << "Video paused";
}

void VideoManager::stopVideo()
{
    if (!m_mediaPlayer) {
        return;
    }

    m_mediaPlayer->stop();
    m_videoWidget->hide();
    qDebug() << "Video stopped";
}

void VideoManager::togglePlayPause()
{
    if (!m_mediaPlayer) {
        return;
    }

    if (m_isPlaying) {
        m_mediaPlayer->pause();
    } else {
        m_mediaPlayer->play();
    }

    qDebug() << "Video play/pause toggled";
}

void VideoManager::showVideoWindow()
{
    if (m_videoWidget) {
        m_videoWidget->show();
        m_videoWidget->raise();
        m_videoWidget->activateWindow();
        qDebug() << "Video window shown";
    }
}

void VideoManager::onPlaybackStateChanged(QMediaPlayer::PlaybackState state)
{
    bool wasPlaying = m_isPlaying;
    m_isPlaying = (state == QMediaPlayer::PlayingState);

    if (wasPlaying != m_isPlaying) {
        emit videoStateChanged(m_isPlaying);
    }

    qDebug() << "Playback state changed:" << state << "isPlaying:" << m_isPlaying;
}

void VideoManager::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    qDebug() << "Media status changed:" << status;
    if (status == QMediaPlayer::InvalidMedia) {
        emit videoError("无效的媒体文件");
    } else if (status == QMediaPlayer::NoMedia) {
        emit videoError("没有媒体文件");
    }
}
