#include "monitorwindow.h"
#include "monitoring_datasource.h"
#include "datascreen.h"
#include "csvreader.h"
#include "smoke_analyzer_comm.h"
#include "config_manager.h"
#include "configmanager_qml.h"
#include "dcsopc.h"
#include "parameter_adjustment.h"
#include "parameter_adjustment_qml.h"
#include "systemtray.h"
#include <thread>

#include <QApplication>
#include <QLocale>
#include <QTranslator>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QDir>
#include <QFile>
#include <QCoreApplication>
#include <QIcon>
#include <QQuickStyle>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

//锅炉列表--全局
std::unordered_map<std::string, Boiler*> boiler_map;

//DCS OPC设备列表--全局
extern std::unordered_map<std::string, DCSOPCDevice*> dcs_opc_map;

//协议文件句柄映射--全局
std::unordered_map<std::string, int> protocol_fd_map;

//全局配置管理器
ConfigManager* g_config_manager = nullptr;

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // 设置应用程序属性
    a.setApplicationName("precalciner");
    a.setApplicationDisplayName("precalciner");
    a.setApplicationVersion("1.0.0");
    a.setOrganizationName("Precalciner Burning System");
    a.setOrganizationDomain("precalciner.com");

    // 设置应用程序图标
    a.setWindowIcon(QIcon(":/images/app-icon.png"));

#ifdef _WIN32
    // 设置Windows控制台支持UTF-8输出
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 设置控制台字体为支持中文的字体
    CONSOLE_FONT_INFOEX cfi;
    cfi.cbSize = sizeof(cfi);
    cfi.nFont = 0;
    cfi.dwFontSize.X = 0;
    cfi.dwFontSize.Y = 16;
    cfi.FontFamily = FF_DONTCARE;
    cfi.FontWeight = FW_NORMAL;
    wcscpy_s(cfi.FaceName, L"Consolas");
    SetCurrentConsoleFontEx(GetStdHandle(STD_OUTPUT_HANDLE), FALSE, &cfi);

    debug_printf("控制台编码设置完成，支持中文显示\n");
#endif

    // 设置Qt Quick样式为Fusion，支持contentItem自定义
    // 这样可以避免"不支持自定义"的警告信息
    QQuickStyle::setStyle("Fusion");
    debug_printf("Qt Quick样式设置为Fusion，支持控件自定义\n");

    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "SmartBurning_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            a.installTranslator(&translator);
            break;
        }
    }

    // 初始化硬件数据采集系统
    //加载配置文件
    debug_printf("主程序: 开始加载配置文件 config.ini\n");
    debug_printf("主程序: 当前工作目录: %s\n", QDir::currentPath().toStdString().c_str());

    // 检查配置文件是否存在
    QString configPath = "config.ini";
    if (!QFile::exists(configPath)) {
        debug_printf("主程序: 配置文件 %s 不存在，尝试使用应用程序目录\n", configPath.toStdString().c_str());
        configPath = QCoreApplication::applicationDirPath() + "/config.ini";
        debug_printf("主程序: 尝试路径: %s\n", configPath.toStdString().c_str());
    }

    // 确保必要的目录存在
    QString dataDir = "data";
    if (!QDir().exists(dataDir)) {
        if (QDir().mkpath(dataDir)) {
            debug_printf("主程序: 创建data目录成功: %s\n", QDir().absoluteFilePath(dataDir).toStdString().c_str());
        } else {
            debug_printf("主程序: 创建data目录失败: %s\n", dataDir.toStdString().c_str());
        }
    } else {
        debug_printf("主程序: data目录已存在: %s\n", QDir().absoluteFilePath(dataDir).toStdString().c_str());
    }

    ConfigManager config(configPath.toStdString());
    bool config_loaded = config.load();

    // 设置全局配置管理器指针
    g_config_manager = &config;
    if (config_loaded) {
        debug_printf("主程序: 配置文件加载成功\n");

        // 测试读取配置
        std::string boiler_list = config.get<std::string>("BoilerList", "list");
        debug_printf("主程序: 读取到的锅炉列表: '%s'\n", boiler_list.c_str());

        std::string protocol_list = config.get<std::string>("ProtocolList", "list");
        debug_printf("主程序: 读取到的协议列表: '%s'\n", protocol_list.c_str());

    } else {
        debug_printf("主程序: 配置文件加载失败\n");
    }

    // 初始化参数调整管理器
    g_parameter_adjustment_manager = new ParameterAdjustmentManager(&config);
    if (g_parameter_adjustment_manager->initialize() == 0) {
        debug_printf("主程序: 参数调整管理器初始化成功\n");
    } else {
        debug_printf("主程序: 参数调整管理器初始化失败\n");
    }

    //启动硬件驱动文件句柄
    protocol_fd_map = start_fd(&config);

    //创建所有锅炉列表
    debug_printf("主程序: 开始创建锅炉列表\n");
    boiler_map = get_boiler_list(&config);
    debug_printf("主程序: 锅炉列表创建完成，数量: %zu\n", boiler_map.size());

    //创建所有DCS OPC设备列表
    debug_printf("主程序: 开始创建DCS OPC设备列表\n");
    dcs_opc_map = get_dcs_opc_list(&config);
    debug_printf("主程序: DCS OPC设备列表创建完成，数量: %zu\n", dcs_opc_map.size());

    //遍历分别启动采集线程
    for (const auto& pair : boiler_map) {
        debug_printf("主程序: 配置锅炉 '%s'\n", pair.first.c_str());
        std::string protocol_name = config.get<std::string>(pair.second->boiler_name, "Protocol");
        debug_printf("主程序: 锅炉 '%s' 使用协议 '%s'\n", pair.first.c_str(), protocol_name.c_str());
        //设置文件句柄
        pair.second->fd = protocol_fd_map[protocol_name];
        debug_printf("主程序: 锅炉 '%s' 文件句柄: %d\n", pair.first.c_str(), pair.second->fd);
        //启动数据采集线程
        pair.second->start_data_collect();
        debug_printf("主程序: 锅炉 '%s' 数据采集线程已启动\n", pair.first.c_str());
    }

    //遍历分别启动DCS OPC设备采集线程（32位程序稳定性优化：单线程模式）
    for (const auto& pair : dcs_opc_map) {
        debug_printf("主程序: 配置DCS OPC设备 '%s'\n", pair.first.c_str());
        //启动统一数据采集线程（包含主要参数和生料量数据）
        pair.second->start_data_collect();
        debug_printf("主程序: DCS OPC设备 '%s' 统一采集线程已启动（32位程序优化：单线程模式）\n", pair.first.c_str());

        //32位程序优化：不再启动生料量独立采集线程，改为统一采集
        pair.second->start_raw_material_collect();  // 此调用现在是空实现，保持接口兼容性
        debug_printf("主程序: DCS OPC设备 '%s' 32位程序优化：生料量采集已合并到统一线程\n", pair.first.c_str());
    }

// DEBUG线程已临时移除，避免启动时的潜在问题
// 如需调试，可以在程序启动后手动查看日志
#ifdef DEBUG_DISABLED
    extern bool ready;
    std::thread{[]{    //此处使用了lambda函数
            debug_printf("\n启动获取实时数据获取debug\n");
            while (!ready){
                float co = 0.00,o2 = 0.00;
                float current = 0.00, voltage =0.00, temperature = 0.00;
                float nox = 0.00, so2 = 0.0;
                int switch1 = 0;  // 开关量信号

                // 遍历所有配置的锅炉进行调试数据获取
                for (const auto& pair : boiler_map) {
                    const std::string& boiler_name = pair.first;
                    get_realtime_data(boiler_name, &co, &o2, &nox, &so2, &current, &voltage, &temperature, &switch1);
                    debug_printf("%s 采集到数据 %.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%d\n",
                           boiler_name.c_str(), co, o2, nox, so2, current, voltage, temperature, switch1);
                }



                // 遍历所有配置的DCS OPC设备进行调试数据获取
                for (const auto& pair : dcs_opc_map) {
                    const std::string& dcs_name = pair.first;
                    float furnace_actual_temp = 0.0f, actual_furnace_pressure = 0.0f;
                    float coal_feed_rate = 0.0f, induced_draft_fan_frequency = 0.0f;

                    // 使用原有接口获取数据（兼容性接口，数据来源相同）
                    float furnace_set_temp = 0.0f, actual_raw_material = 0.0f, planned_raw_material = 0.0f;
                    get_realtime_dcs_opc_data(dcs_name, &furnace_set_temp, &furnace_actual_temp, &actual_furnace_pressure,
                                             &coal_feed_rate, &actual_raw_material, &planned_raw_material,
                                             &induced_draft_fan_frequency);
                    debug_printf("%s DCS OPC采集到分解炉数据 炉膛实际温度=%.2f, 实际炉压=%.2f, 给煤量=%.2f, 实际生料量=%.2f\n",
                           dcs_name.c_str(), furnace_actual_temp, actual_furnace_pressure, coal_feed_rate, actual_raw_material);
                    debug_printf("%s DCS OPC采集到风机数据 引风机频率=%.2f\n",
                           dcs_name.c_str(), induced_draft_fan_frequency);
                }

               std::this_thread::sleep_for(std::chrono::seconds(3));
            }
            debug_printf("\n退出获取实时数据debug\n");
      }
    }.detach();
#endif
    
    // 创建监控窗口实例
    MonitorWindow monitorWindow;

    // 创建数据大屏实例
    DataScreen dataScreen;

    // 创建CSV读取器实例
    CsvReader csvReader;

    // 创建配置管理器QML包装实例
    ConfigManagerQML configManagerQML;
    configManagerQML.setConfigManager(&config);

    // 创建参数调整QML包装实例
    ParameterAdjustmentQML parameterAdjustmentQML;

    // 设置全局QML包装类指针（用于在parameter_adjustment.cpp中发出信号）
    extern ParameterAdjustmentQML* g_parameter_adjustment_qml;
    g_parameter_adjustment_qml = &parameterAdjustmentQML;

    // 创建系统托盘实例
    SystemTray systemTray;

    // 创建QML引擎
    QQmlApplicationEngine engine;

    // 设置系统托盘的QML引擎引用
    systemTray.setQmlEngine(&engine);

    // 将实例注册到QML上下文
    engine.rootContext()->setContextProperty("monitorWindow", &monitorWindow);
    engine.rootContext()->setContextProperty("dataScreen", &dataScreen);
    engine.rootContext()->setContextProperty("csvReader", &csvReader);
    engine.rootContext()->setContextProperty("configManagerQML", &configManagerQML);
    engine.rootContext()->setContextProperty("parameterAdjustmentQML", &parameterAdjustmentQML);
    engine.rootContext()->setContextProperty("systemTray", &systemTray);
    
    // 加载主QML文件
    debug_printf("开始加载QML文件...\n");

    // 添加QML错误处理
    QObject::connect(&engine, &QQmlApplicationEngine::objectCreated,
                     [](QObject *obj, const QUrl &objUrl) {
        if (!obj) {
            debug_printf("QML对象创建失败: %s\n", objUrl.toString().toStdString().c_str());
        } else {
            debug_printf("QML对象创建成功: %s\n", objUrl.toString().toStdString().c_str());
        }
    });

    engine.load(QUrl(QStringLiteral("qrc:/main.qml")));
    debug_printf("QML文件加载命令已执行\n");

    if (engine.rootObjects().isEmpty()) {
        debug_printf("错误: QML根对象为空，程序退出\n");
        return -1;
    }

    debug_printf("QML文件加载成功，根对象数量: %d\n", engine.rootObjects().size());

    // 显示系统托盘图标
    if (systemTray.isSystemTrayAvailable()) {
        systemTray.setVisible(true);
        debug_printf("系统托盘已启用\n");
    } else {
        debug_printf("系统托盘不可用\n");
    }

    // 设置应用程序退出策略：只有在明确调用quit时才退出
    a.setQuitOnLastWindowClosed(false);

    return a.exec();
}
