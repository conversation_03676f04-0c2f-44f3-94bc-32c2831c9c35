import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: secondPasswordInput
    
    property string targetModel: ""
    
    // 信号定义
    signal navigateBack()
    signal navigateToConfirmation(string targetModel)
    
    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回"
                onClicked: secondPasswordInput.navigateBack()
            }
            Label {
                text: "输入第二人密码"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }
        }
    }
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 60
        spacing: 40

        Item {
            Layout.fillHeight: true
        }

        Label {
            text: "请输入第二人密码:"
            font.pixelSize: 24
            font.bold: true
            Layout.alignment: Qt.AlignHCenter
            color: "#ffffff"
        }

        Label {
            text: "目标模型: " + targetModel
            font.pixelSize: 18
            color: "#cccccc"
            Layout.alignment: Qt.AlignHCenter
        }

        Label {
            text: "第一人密码验证已通过，现在需要第二人确认"
            font.pixelSize: 16
            color: "#2196F3"
            wrapMode: Text.WordWrap
            Layout.fillWidth: true
            Layout.maximumWidth: 600
            Layout.alignment: Qt.AlignHCenter
            horizontalAlignment: Text.AlignHCenter
        }

        Rectangle {
            Layout.fillWidth: true
            Layout.maximumWidth: 600
            Layout.alignment: Qt.AlignHCenter
            height: 80
            color: "#f5f5f5"
            radius: 8
            border.color: secondPasswordField.activeFocus ? "#2196f3" : "#ddd"
            border.width: 2

            RowLayout {
                anchors.fill: parent
                anchors.margins: 15

                TextField {
                    id: secondPasswordField
                    Layout.fillWidth: true
                    placeholderText: "请输入第二人密码"
                    echoMode: TextInput.Password
                    font.pixelSize: 18
                    background: Rectangle {
                        color: "transparent"
                    }

                    onTextChanged: {
                        errorLabel.visible = false
                    }
                }

                Button {
                    text: "清空"
                    Layout.preferredWidth: 80
                    Layout.preferredHeight: 40
                    onClicked: secondPasswordField.clear()
                    background: Rectangle {
                        color: parent.pressed ? "#f0f0f0" : "transparent"
                        radius: 4
                    }

                    contentItem: Text {
                        text: parent.text
                        color: "#666"
                        font.pixelSize: 16
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }

        Label {
            id: errorLabel
            color: "red"
            visible: false
            text: "第二人密码错误，请重新输入"
            font.pixelSize: 18
            Layout.alignment: Qt.AlignHCenter
        }

        Label {
            text: "注意：第二人密码验证失败将返回到第二人密码输入步骤"
            font.pixelSize: 16
            color: "#cccccc"
            wrapMode: Text.WordWrap
            Layout.fillWidth: true
            Layout.maximumWidth: 600
            Layout.alignment: Qt.AlignHCenter
            horizontalAlignment: Text.AlignHCenter
        }

        Item {
            Layout.fillHeight: true
        }

        RowLayout {
            Layout.alignment: Qt.AlignHCenter
            spacing: 40

            Button {
                text: "取消"
                Layout.preferredWidth: 150
                Layout.preferredHeight: 60
                onClicked: secondPasswordInput.navigateBack()

                background: Rectangle {
                    color: parent.pressed ? "#f0f0f0" : "#ffffff"
                    border.color: "#ddd"
                    border.width: 2
                    radius: 8
                }

                contentItem: Text {
                    text: parent.text
                    color: "#333"
                    font.pixelSize: 18
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            Button {
                text: "确认"
                Layout.preferredWidth: 150
                Layout.preferredHeight: 60
                enabled: secondPasswordField.text.length > 0
                onClicked: {
                    if (modelManager.verifySecondPassword(secondPasswordField.text)) {
                        secondPasswordInput.navigateToConfirmation(targetModel)
                        errorLabel.visible = false
                        secondPasswordField.clear()
                    } else {
                        errorLabel.visible = true
                        secondPasswordField.selectAll()
                    }
                }

                background: Rectangle {
                    color: parent.enabled ? (parent.pressed ? "#1976d2" : "#2196f3") : "#cccccc"
                    radius: 8
                }

                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 18
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }
}
