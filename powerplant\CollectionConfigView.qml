import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: configPage
    
    // 信号定义
    signal navigateBack()
    
    // 本地状态管理
    property var rs485Config: ({})
    property var dcsRS485Config: ({})
    property bool hasUnsavedChanges: false
    
    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }
    
    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回监控"
                onClicked: {
                    if (hasUnsavedChanges) {
                        confirmDialog.open()
                    } else {
                        stackView.pop()
                    }
                }
            }
            Label {
                text: "采集配置管理"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
                color: "white"
            }
            
            Button {
                text: "重新加载"
                onClicked: {
                    loadConfigs()
                }
                background: Rectangle {
                    color: parent.pressed ? "#ff9800" : "#ffc107"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            
            Button {
                text: "保存配置"
                enabled: hasUnsavedChanges
                onClicked: {
                    saveConfigs()
                }
                background: Rectangle {
                    color: parent.enabled ? (parent.pressed ? "#388e3c" : "#4caf50") : "#666666"
                    radius: 4
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 16
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }
    
    // 确认对话框
    Dialog {
        id: confirmDialog
        anchors.centerIn: parent
        width: 400
        height: 200
        title: "未保存的更改"
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20
            
            Label {
                text: "您有未保存的配置更改，是否要保存？"
                font.pixelSize: 16
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
            }
            
            RowLayout {
                Layout.fillWidth: true
                
                Button {
                    text: "保存并返回"
                    Layout.fillWidth: true
                    onClicked: {
                        saveConfigs()
                        confirmDialog.close()
                        stackView.pop()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#388e3c" : "#4caf50"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    text: "不保存"
                    Layout.fillWidth: true
                    onClicked: {
                        confirmDialog.close()
                        stackView.pop()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#d32f2f" : "#f44336"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    text: "取消"
                    Layout.fillWidth: true
                    onClicked: {
                        confirmDialog.close()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#757575" : "#9e9e9e"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
    }

    // 重启数据采集确认对话框
    Dialog {
        id: restartDialog
        anchors.centerIn: parent
        width: 500
        height: 250
        title: "配置保存成功"

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20

            Label {
                text: "✅ 串口配置已成功保存到config.ini文件！"
                font.pixelSize: 16
                font.bold: true
                color: "#4caf50"
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
            }

            Label {
                text: "为了使新的串口配置生效，需要重启数据采集系统。\n这将会：\n• 停止当前所有数据采集线程\n• 关闭现有串口连接\n• 使用新配置重新建立串口连接\n• 重新启动数据采集\n\n是否现在重启数据采集系统？"
                font.pixelSize: 14
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
                color: "#333333"
            }

            RowLayout {
                Layout.fillWidth: true

                Button {
                    text: "立即重启"
                    Layout.fillWidth: true
                    onClicked: {
                        restartDialog.close()
                        restartDataCollection()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#388e3c" : "#4caf50"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        font.bold: true
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }

                Button {
                    text: "稍后手动重启"
                    Layout.fillWidth: true
                    onClicked: {
                        restartDialog.close()
                        // 显示提示消息
                        successMessage.text = "配置已保存！请手动重启软件或点击'重新加载'按钮使配置生效。"
                        successMessage.visible = true
                        successTimer.start()
                    }
                    background: Rectangle {
                        color: parent.pressed ? "#757575" : "#9e9e9e"
                        radius: 4
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 14
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
    }

    // 主要内容区域
    ScrollView {
        anchors.fill: parent
        anchors.margins: 20
        
        ColumnLayout {
            width: parent.width
            spacing: 30
            
            // RS485配置区域
            Rectangle {
                Layout.fillWidth: true
                height: rs485Column.implicitHeight + 40
                color: "#ffffff"
                radius: 12
                border.color: "#e0e0e0"
                border.width: 1
                
                ColumnLayout {
                    id: rs485Column
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 15
                    
                    Label {
                        text: "RS485 烟气分析仪配置"
                        font.pixelSize: 18
                        font.bold: true
                        color: "#333333"
                    }
                    
                    GridLayout {
                        Layout.fillWidth: true
                        columns: 4
                        columnSpacing: 20
                        rowSpacing: 15
                        
                        // 串口号
                        Label {
                            text: "串口号:"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        ComboBox {
                            id: rs485PortCombo
                            Layout.preferredWidth: 120
                            editable: true
                            model: ["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "COM10"]
                            currentIndex: {
                                var value = rs485Config.Port || "COM5"
                                var index = model.indexOf(value)
                                return index >= 0 ? index : -1
                            }
                            editText: rs485Config.Port || "COM5"
                            onCurrentTextChanged: {
                                if (currentText !== (rs485Config.Port || "COM5")) {
                                    hasUnsavedChanges = true
                                }
                            }
                            onEditTextChanged: {
                                if (editText !== (rs485Config.Port || "COM5")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }

                        // 波特率
                        Label {
                            text: "波特率:"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        ComboBox {
                            id: rs485BaudRateCombo
                            Layout.preferredWidth: 120
                            model: ["1200", "2400", "4800", "9600", "19200", "38400", "57600", "115200"]
                            currentIndex: {
                                var value = rs485Config.BaudRate || "9600"
                                return model.indexOf(value)
                            }
                            onCurrentTextChanged: {
                                if (currentText !== (rs485Config.BaudRate || "9600")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }
                        
                        // 停止位
                        Label {
                            text: "停止位:"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        ComboBox {
                            id: rs485StopBitsCombo
                            Layout.preferredWidth: 120
                            model: ["1", "2"]
                            currentIndex: {
                                var value = rs485Config.StopBits || "1"
                                return model.indexOf(value)
                            }
                            onCurrentTextChanged: {
                                if (currentText !== (rs485Config.StopBits || "1")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }
                        
                        // 校验位
                        Label {
                            text: "校验位:"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        ComboBox {
                            id: rs485ParityCombo
                            Layout.preferredWidth: 120
                            model: ["N", "E", "O"]
                            currentIndex: {
                                var value = rs485Config.Parity || "N"
                                return model.indexOf(value)
                            }
                            onCurrentTextChanged: {
                                if (currentText !== (rs485Config.Parity || "N")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }
                        
                        // 数据位
                        Label {
                            text: "数据位:"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        ComboBox {
                            id: rs485DataBitsCombo
                            Layout.preferredWidth: 120
                            model: ["7", "8"]
                            currentIndex: {
                                var value = rs485Config.DataBits || "8"
                                return model.indexOf(value)
                            }
                            onCurrentTextChanged: {
                                if (currentText !== (rs485Config.DataBits || "8")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }
                        
                        // 超时时间
                        Label {
                            text: "超时时间(s):"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        TextField {
                            id: rs485TimeoutField
                            Layout.preferredWidth: 120
                            text: rs485Config.Timeout || ""
                            validator: DoubleValidator { bottom: 0.1; top: 10.0; decimals: 1 }
                            onTextChanged: {
                                if (text !== (rs485Config.Timeout || "")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }
                    }
                }
            }

            // DCS_RS485配置区域
            Rectangle {
                Layout.fillWidth: true
                height: dcsRS485Column.implicitHeight + 40
                color: "#ffffff"
                radius: 12
                border.color: "#e0e0e0"
                border.width: 1

                ColumnLayout {
                    id: dcsRS485Column
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 15

                    Label {
                        text: "DCS_RS485 分布式控制系统配置"
                        font.pixelSize: 18
                        font.bold: true
                        color: "#333333"
                    }

                    GridLayout {
                        Layout.fillWidth: true
                        columns: 4
                        columnSpacing: 20
                        rowSpacing: 15

                        // 串口号
                        Label {
                            text: "串口号:"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        ComboBox {
                            id: dcsPortCombo
                            Layout.preferredWidth: 120
                            editable: true
                            model: ["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "COM10"]
                            currentIndex: {
                                var value = dcsRS485Config.Port || "COM6"
                                var index = model.indexOf(value)
                                return index >= 0 ? index : -1
                            }
                            editText: dcsRS485Config.Port || "COM6"
                            onCurrentTextChanged: {
                                if (currentText !== (dcsRS485Config.Port || "COM6")) {
                                    hasUnsavedChanges = true
                                }
                            }
                            onEditTextChanged: {
                                if (editText !== (dcsRS485Config.Port || "COM6")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }

                        // 波特率
                        Label {
                            text: "波特率:"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        ComboBox {
                            id: dcsBaudRateCombo
                            Layout.preferredWidth: 120
                            model: ["1200", "2400", "4800", "9600", "19200", "38400", "57600", "115200"]
                            currentIndex: {
                                var value = dcsRS485Config.BaudRate || "9600"
                                return model.indexOf(value)
                            }
                            onCurrentTextChanged: {
                                if (currentText !== (dcsRS485Config.BaudRate || "9600")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }

                        // 停止位
                        Label {
                            text: "停止位:"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        ComboBox {
                            id: dcsStopBitsCombo
                            Layout.preferredWidth: 120
                            model: ["1", "2"]
                            currentIndex: {
                                var value = dcsRS485Config.StopBits || "1"
                                return model.indexOf(value)
                            }
                            onCurrentTextChanged: {
                                if (currentText !== (dcsRS485Config.StopBits || "1")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }

                        // 校验位
                        Label {
                            text: "校验位:"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        ComboBox {
                            id: dcsParityCombo
                            Layout.preferredWidth: 120
                            model: ["N", "E", "O"]
                            currentIndex: {
                                var value = dcsRS485Config.Parity || "N"
                                return model.indexOf(value)
                            }
                            onCurrentTextChanged: {
                                if (currentText !== (dcsRS485Config.Parity || "N")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }

                        // 数据位
                        Label {
                            text: "数据位:"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        ComboBox {
                            id: dcsDataBitsCombo
                            Layout.preferredWidth: 120
                            model: ["7", "8"]
                            currentIndex: {
                                var value = dcsRS485Config.DataBits || "8"
                                return model.indexOf(value)
                            }
                            onCurrentTextChanged: {
                                if (currentText !== (dcsRS485Config.DataBits || "8")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }

                        // 超时时间
                        Label {
                            text: "超时时间(s):"
                            font.pixelSize: 14
                            color: "#666666"
                        }
                        TextField {
                            id: dcsTimeoutField
                            Layout.preferredWidth: 120
                            text: dcsRS485Config.Timeout || ""
                            validator: DoubleValidator { bottom: 0.1; top: 10.0; decimals: 1 }
                            onTextChanged: {
                                if (text !== (dcsRS485Config.Timeout || "")) {
                                    hasUnsavedChanges = true
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 成功消息
    Rectangle {
        id: successMessage
        anchors.top: parent.top
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.topMargin: 20
        width: 300
        height: 50
        color: "#4caf50"
        radius: 8
        visible: false
        z: 1000

        property string text: ""

        Label {
            id: successLabel
            anchors.centerIn: parent
            text: successMessage.text
            color: "white"
            font.pixelSize: 16
            font.bold: true
        }
    }

    // 错误消息
    Rectangle {
        id: errorMessage
        anchors.top: parent.top
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.topMargin: 20
        width: 300
        height: 50
        color: "#f44336"
        radius: 8
        visible: false
        z: 1000

        property string text: ""

        Label {
            id: errorLabel
            anchors.centerIn: parent
            text: errorMessage.text
            color: "white"
            font.pixelSize: 16
            font.bold: true
        }
    }

    // 成功消息定时器
    Timer {
        id: successTimer
        interval: 3000
        onTriggered: successMessage.visible = false
    }

    // 错误消息定时器
    Timer {
        id: errorTimer
        interval: 3000
        onTriggered: errorMessage.visible = false
    }

    // 页面初始化时加载配置
    Component.onCompleted: {
        loadConfigs()
    }

    // 加载配置的函数
    function loadConfigs() {
        if (typeof configManagerQML !== 'undefined') {
            rs485Config = configManagerQML.getRS485Config()
            dcsRS485Config = configManagerQML.getDCSRS485Config()
            hasUnsavedChanges = false
        }
    }

    // 保存配置的函数
    function saveConfigs() {
        if (typeof configManagerQML !== 'undefined') {
            // 收集RS485配置
            var newRS485Config = {
                "Port": rs485PortCombo.editable ? rs485PortCombo.editText : rs485PortCombo.currentText,
                "BaudRate": rs485BaudRateCombo.currentText,
                "StopBits": rs485StopBitsCombo.currentText,
                "Parity": rs485ParityCombo.currentText,
                "DataBits": rs485DataBitsCombo.currentText,
                "Timeout": rs485TimeoutField.text
            }

            // 收集DCS_RS485配置
            var newDCSRS485Config = {
                "Port": dcsPortCombo.editable ? dcsPortCombo.editText : dcsPortCombo.currentText,
                "BaudRate": dcsBaudRateCombo.currentText,
                "StopBits": dcsStopBitsCombo.currentText,
                "Parity": dcsParityCombo.currentText,
                "DataBits": dcsDataBitsCombo.currentText,
                "Timeout": dcsTimeoutField.text
            }

            // 保存配置
            var rs485Success = configManagerQML.saveRS485Config(newRS485Config)
            var dcsSuccess = configManagerQML.saveDCSRS485Config(newDCSRS485Config)

            if (rs485Success && dcsSuccess) {
                hasUnsavedChanges = false
                rs485Config = newRS485Config
                dcsRS485Config = newDCSRS485Config

                // 显示配置保存成功，询问是否重启数据采集
                restartDialog.open()
            } else {
                // 显示错误消息
                errorMessage.text = "配置保存失败，请检查输入参数！"
                errorMessage.visible = true
                errorTimer.start()
            }
        }
    }

    // 重启数据采集的函数
    function restartDataCollection() {
        if (typeof configManagerQML !== 'undefined') {
            // 显示重启进度消息
            successMessage.text = "正在重启数据采集系统，请稍候..."
            successMessage.visible = true

            // 调用重启功能
            var success = configManagerQML.restartDataCollection()

            if (success) {
                successMessage.text = "✅ 数据采集系统重启成功！新的串口配置已生效。"
                successMessage.visible = true
                successTimer.start()
            } else {
                errorMessage.text = "❌ 数据采集系统重启失败！请检查串口配置或手动重启软件。"
                errorMessage.visible = true
                errorTimer.start()
            }
        }
    }

}
