#ifndef SYSTEMTRAY_H
#define SYSTEMTRAY_H

#include <QObject>
#include <QSystemTrayIcon>
#include <QMenu>
#include <QAction>
#include <QApplication>
#include <QQmlApplicationEngine>

class SystemTray : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool available READ isSystemTrayAvailable CONSTANT)
    Q_PROPERTY(bool visible READ isVisible WRITE setVisible NOTIFY visibilityChanged)

public:
    explicit SystemTray(QObject *parent = nullptr);
    ~SystemTray();

    // 系统托盘是否可用
    bool isSystemTrayAvailable() const;
    
    // 托盘图标是否可见
    bool isVisible() const;
    void setVisible(bool visible);

    // 设置QML引擎引用（用于控制窗口显示/隐藏）
    void setQmlEngine(QQmlApplicationEngine *engine);

public slots:
    // 显示主窗口
    void showMainWindow();
    
    // 隐藏主窗口到托盘
    void hideToTray();
    
    // 退出应用程序
    void quitApplication();
    
    // 显示托盘消息
    void showTrayMessage(const QString &title, const QString &message);

signals:
    void visibilityChanged();
    void trayIconActivated();
    void showWindowRequested();
    void quitRequested();

private slots:
    void onTrayIconActivated(QSystemTrayIcon::ActivationReason reason);

private:
    void createTrayIcon();
    void createTrayMenu();

    QSystemTrayIcon *m_trayIcon;
    QMenu *m_trayMenu;
    QAction *m_showAction;
    QAction *m_quitAction;
    QQmlApplicationEngine *m_qmlEngine;
};

#endif // SYSTEMTRAY_H
