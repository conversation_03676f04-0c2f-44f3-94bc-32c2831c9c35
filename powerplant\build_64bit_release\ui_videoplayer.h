/********************************************************************************
** Form generated from reading UI file 'videoplayer.ui'
**
** Created by: Qt User Interface Compiler version 6.9.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_VIDEOPLAYER_H
#define UI_VIDEOPLAYER_H

#include <QtCore/QVariant>
#include <QtMultimediaWidgets/QVideoWidget>
#include <QtWidgets/QApplication>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSlider>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_VideoPlayer
{
public:
    QVBoxLayout *verticalLayout;
    QVideoWidget *videoWidget;
    QLabel *errorLabel;
    QHBoxLayout *horizontalLayout;
    QLabel *currentTimeLabel;
    QSlider *positionSlider;
    QLabel *totalTimeLabel;
    QPushButton *playButton;

    void setupUi(QWidget *VideoPlayer)
    {
        if (VideoPlayer->objectName().isEmpty())
            VideoPlayer->setObjectName("VideoPlayer");
        VideoPlayer->resize(400, 300);
        verticalLayout = new QVBoxLayout(VideoPlayer);
        verticalLayout->setSpacing(0);
        verticalLayout->setContentsMargins(11, 11, 11, 11);
        verticalLayout->setObjectName("verticalLayout");
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        videoWidget = new QVideoWidget(VideoPlayer);
        videoWidget->setObjectName("videoWidget");
        QSizePolicy sizePolicy(QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(videoWidget->sizePolicy().hasHeightForWidth());
        videoWidget->setSizePolicy(sizePolicy);

        verticalLayout->addWidget(videoWidget);

        errorLabel = new QLabel(VideoPlayer);
        errorLabel->setObjectName("errorLabel");
        errorLabel->setEnabled(true);
        sizePolicy.setHeightForWidth(errorLabel->sizePolicy().hasHeightForWidth());
        errorLabel->setSizePolicy(sizePolicy);
        errorLabel->setStyleSheet(QString::fromUtf8("QLabel {\n"
"	color: red;\n"
"	background-color: white;\n"
"	padding: 10px;\n"
"	font-size: 30px;\n"
"	text-align: center;\n"
"}"));
        errorLabel->setAlignment(Qt::AlignmentFlag::AlignCenter);

        verticalLayout->addWidget(errorLabel);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setSpacing(4);
        horizontalLayout->setObjectName("horizontalLayout");
        horizontalLayout->setContentsMargins(6, -1, 6, 0);
        currentTimeLabel = new QLabel(VideoPlayer);
        currentTimeLabel->setObjectName("currentTimeLabel");
        QSizePolicy sizePolicy1(QSizePolicy::Policy::Fixed, QSizePolicy::Policy::Fixed);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(currentTimeLabel->sizePolicy().hasHeightForWidth());
        currentTimeLabel->setSizePolicy(sizePolicy1);
        currentTimeLabel->setStyleSheet(QString::fromUtf8("color:#ffffff"));

        horizontalLayout->addWidget(currentTimeLabel);

        positionSlider = new QSlider(VideoPlayer);
        positionSlider->setObjectName("positionSlider");
        positionSlider->setMinimumSize(QSize(0, 30));
        positionSlider->setMaximumSize(QSize(16777215, 300));
        positionSlider->setOrientation(Qt::Orientation::Horizontal);

        horizontalLayout->addWidget(positionSlider);

        totalTimeLabel = new QLabel(VideoPlayer);
        totalTimeLabel->setObjectName("totalTimeLabel");
        sizePolicy1.setHeightForWidth(totalTimeLabel->sizePolicy().hasHeightForWidth());
        totalTimeLabel->setSizePolicy(sizePolicy1);
        totalTimeLabel->setStyleSheet(QString::fromUtf8("color:#ffffff"));

        horizontalLayout->addWidget(totalTimeLabel);

        playButton = new QPushButton(VideoPlayer);
        playButton->setObjectName("playButton");
        QSizePolicy sizePolicy2(QSizePolicy::Policy::Fixed, QSizePolicy::Policy::Minimum);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(playButton->sizePolicy().hasHeightForWidth());
        playButton->setSizePolicy(sizePolicy2);

        horizontalLayout->addWidget(playButton);


        verticalLayout->addLayout(horizontalLayout);


        retranslateUi(VideoPlayer);

        QMetaObject::connectSlotsByName(VideoPlayer);
    } // setupUi

    void retranslateUi(QWidget *VideoPlayer)
    {
        VideoPlayer->setWindowTitle(QCoreApplication::translate("VideoPlayer", "Widget", nullptr));
        errorLabel->setText(QCoreApplication::translate("VideoPlayer", "Error Label", nullptr));
        currentTimeLabel->setText(QCoreApplication::translate("VideoPlayer", "00:00", nullptr));
        totalTimeLabel->setText(QCoreApplication::translate("VideoPlayer", "--:--", nullptr));
        playButton->setText(QCoreApplication::translate("VideoPlayer", "\346\232\202\345\201\234", nullptr));
    } // retranslateUi

};

namespace Ui {
    class VideoPlayer: public Ui_VideoPlayer {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_VIDEOPLAYER_H
