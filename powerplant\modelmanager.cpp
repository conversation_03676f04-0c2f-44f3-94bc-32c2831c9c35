#include "modelmanager.h"
#include <QDebug>

ModelManager::ModelManager(QObject *parent)
    : QObject(parent), m_isLoading(false)
{
    // 模拟数据 - 更详细的模型信息
    m_availableModels << "智能燃烧模型 v1.0" << "高效燃烧模型 v2.1"
                      << "节能燃烧模型 v1.5" << "精准燃烧模型 v3.0"
                      << "实验燃烧模型 v0.8";
    m_currentModel = "智能燃烧模型 v1.0";

    // 初始化加载定时器
    m_loadingTimer = new QTimer(this);
    m_loadingTimer->setSingleShot(true);
    connect(m_loadingTimer, &QTimer::timeout, this, &ModelManager::onModelLoadingFinished);
}

QStringList ModelManager::availableModels() const
{
    return m_availableModels;
}

QString ModelManager::currentModel() const
{
    return m_currentModel;
}

bool ModelManager::isLoading() const
{
    return m_isLoading;
}

bool ModelManager::verifyPassword(const QString &password)
{
    // 模拟密码验证，实际应用中应使用安全的验证方法
    qDebug() << "验证第一人密码:" << password;
    return password == "123456";
}

bool ModelManager::verifySecondPassword(const QString &password)
{
    // 模拟第二人密码验证
    qDebug() << "验证第二人密码:" << password;
    return password == "654321";
}

void ModelManager::switchModel(const QString &modelName)
{
    if (!m_availableModels.contains(modelName) || m_currentModel == modelName) {
        emit modelSwitchCompleted(false, "无效的模型或模型已是当前使用模型");
        return;
    }

    if (m_isLoading) {
        emit modelSwitchCompleted(false, "模型正在加载中，请稍后再试");
        return;
    }

    m_targetModel = modelName;
    m_isLoading = true;
    emit isLoadingChanged();

    qDebug() << "开始切换模型从" << m_currentModel << "到" << modelName;

    // 模拟模型加载时间（3-5秒）
    m_loadingTimer->start(4000);
}

QString ModelManager::getModelDescription(const QString &modelName)
{
    if (modelName == "智能燃烧模型 v1.0") {
        return "基础智能燃烧控制模型，适用于常规燃烧场景";
    } else if (modelName == "高效燃烧模型 v2.1") {
        return "高效率燃烧优化模型，提升燃烧效率20%";
    } else if (modelName == "节能燃烧模型 v1.5") {
        return "节能型燃烧控制模型，降低能耗15%";
    } else if (modelName == "精准燃烧模型 v3.0") {
        return "高精度燃烧控制模型，精度提升30%";
    } else if (modelName == "实验燃烧模型 v0.8") {
        return "实验性燃烧模型，仅供测试使用";
    }
    return "暂无描述";
}

void ModelManager::onModelLoadingFinished()
{
    m_currentModel = m_targetModel;
    m_isLoading = false;

    emit currentModelChanged();
    emit isLoadingChanged();
    emit modelSwitchCompleted(true, "模型切换成功");

    qDebug() << "模型切换完成，当前模型:" << m_currentModel;
}
