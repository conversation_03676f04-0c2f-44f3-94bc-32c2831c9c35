﻿#include "videoplayer.h"
#include "ui_videoplayer.h"
#include <QMainWindow>
#include <QMediaPlayer>
#include <QVideoWidget>
#include <QPushButton>
#include <QFileDialog>
#include <QHBoxLayout>
#include <QSlider>
#include <QLabel>
#include <QToolTip>

VideoPlayer::VideoPlayer(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::VideoPlayer)
{
    ui->setupUi(this);

    isUserDragging = false;
    isPause = false;

    player = new QMediaPlayer(this);
    player->setVideoOutput(ui->videoWidget);

    connect(ui->playButton, &QPushButton::clicked, this, &VideoPlayer::switchPlayStation);
    connect(player, &QMediaPlayer::positionChanged, this, &VideoPlayer::updateCurrentTime);
    connect(player, &QMediaPlayer::durationChanged, this, &VideoPlayer::updateTotalTime);
    connect(player, &QMediaPlayer::error, this, &VideoPlayer::handleError);
    connect(ui->positionSlider, &QSlider::sliderMoved, player, &QMediaPlayer::setPosition);

    connect(ui->positionSlider, &QSlider::sliderPressed, this, &VideoPlayer::onSliderPressed);
    connect(ui->positionSlider, &QSlider::sliderReleased, this, &VideoPlayer::onSliderReleased);
    connect(ui->positionSlider, &QSlider::valueChanged, this, &VideoPlayer::showTimeTooltip);

}

VideoPlayer::~VideoPlayer()
{
    delete ui;
}

void VideoPlayer::setVideoShow(QString path)
{
    QFileInfo fileInfo(path);
    if (!fileInfo.exists()) {
        ui->errorLabel->setText("错误：文件不存在");
        ui->errorLabel->setVisible(true);
        ui->videoWidget->hide();
        ui->playButton->setEnabled(false);
        ui->positionSlider->setEnabled(false);
        return;
    } else {
        ui->errorLabel->setVisible(false);
    }

    player->setSource(QUrl::fromLocalFile(path));
    player->play();
}

void VideoPlayer::setHttpVideoShow(QString path)
{
    ui->errorLabel->setVisible(false);

    player->setSource(QUrl(path));
    player->play();
}

QString VideoPlayer::formatTime(qint64 ms) const {
    qint64 seconds = ms / 1000;
    qint64 minutes = seconds / 60;
    seconds %= 60;
    return QString("%1:%2").arg(minutes, 2, 10, QLatin1Char('0'))
                         .arg(seconds, 2, 10, QLatin1Char('0'));
}

void VideoPlayer::switchPlayStation()
{
    if (!isPause) {
        ui->playButton->setText("播放");
        player->pause();
    } else if (isPause){
        ui->playButton->setText("暂停");
        player->play();
    }
    isPause = !isPause;
}

void VideoPlayer::updateCurrentTime(qint64 position) {
    ui->positionSlider->setValue(position);
    ui->currentTimeLabel->setText(formatTime(position));
}

void VideoPlayer::updateTotalTime(qint64 duration) {
    if (duration > 0) {
        ui->totalTimeLabel->setText(formatTime(duration));
        ui->positionSlider->setRange(0, duration);
    } else {
        ui->totalTimeLabel->setText("--:--");
        ui->positionSlider->setRange(0, 0);
    }
}

void VideoPlayer::handleError() {
    ui->errorLabel->setText("错误: 文件无法播放");
    ui->errorLabel->setVisible(true);
    ui->videoWidget->hide();
    ui->playButton->setEnabled(false);
    ui->positionSlider->setEnabled(false);
}

void VideoPlayer::onSliderPressed() {
    isUserDragging = true;
    player->pause();
}

void VideoPlayer::onSliderReleased() {
    isUserDragging = false;
    QToolTip::hideText();
    if(isPause) {
        player->pause();
    } else {
        player->play();
    }
}

void VideoPlayer::showTimeTooltip(int position) {
    if (!isUserDragging) return;

    QString timeStr = formatTime(position);

    // 计算提示位置
    QPoint sliderPos = ui->positionSlider->mapToGlobal(QPoint(0, 0));
    int sliderWidth = ui->positionSlider->width();
    int valueRange = ui->positionSlider->maximum() - ui->positionSlider->minimum();
    int relativeX = (position - ui->positionSlider->minimum()) * sliderWidth / valueRange;

    QPoint tooltipPos = sliderPos + QPoint(relativeX-20, -30); // 显示在滑块上方

    // 显示提示
    QToolTip::showText(tooltipPos, timeStr, ui->positionSlider);
}
