#ifndef CSVREADER_H
#define CSVREADER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QVariantList>
#include <QVariantMap>
#include <QDateTime>

class CsvReader : public QObject
{
    Q_OBJECT

public:
    explicit CsvReader(QObject *parent = nullptr);

    // 读取指定日期的CSV文件数据
    Q_INVOKABLE QVariantList readDataByDate(const QString &boilerName, const QDate &date);
    
    // 读取指定日期范围的CSV文件数据
    Q_INVOKABLE QVariantList readDataByDateRange(const QString &boilerName, const QDate &startDate, const QDate &endDate);
    
    // 获取可用的数据日期列表
    Q_INVOKABLE QStringList getAvailableDates(const QString &boilerName);
    
    // 检查指定日期是否有数据
    Q_INVOKABLE bool hasDataForDate(const QString &boilerName, const QDate &date);

private:
    // 构建CSV文件路径
    QString buildCsvFilePath(const QString &boilerName, const QDate &date);
    
    // 解析CSV行数据
    QVariantMap parseCsvLine(const QString &line);
    
    // 验证CSV文件是否存在且可读
    bool isValidCsvFile(const QString &filePath);
};

#endif // CSVREADER_H
