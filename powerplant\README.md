# 智慧燃烧锅炉监控系统 (SmartBurning)

## 项目简介

智慧燃烧锅炉监控系统是一个基于Qt框架开发的工业锅炉燃烧数据实时监控系统。该系统通过RS485串口通信协议采集锅炉运行数据，提供实时数据监控、历史数据查看、视频监控等功能，为锅炉运行管理提供全面的数据支持和可视化界面。

## 主要功能特性

### 🔥 实时数据监控
- 实时采集锅炉燃烧参数（O2、CO、NOx、SO2、温度、电压、电流）
- 支持多锅炉同时监控
- 可配置的数据采集间隔
- 实时数据图表显示

### 📊 数据存储与分析
- 自动将采集数据保存为CSV格式
- 支持历史数据查询和导出
- 日历式数据查看功能
- 数据统计和趋势分析

### 🖥️ 用户界面
- 基于QML的现代化用户界面
- 多页面导航系统
- 实时数据大屏显示
- 参数调整界面

### 📹 视频监控
- 集成视频播放功能
- 支持多媒体文件播放
- 视频监控界面

### ⚙️ 系统配置
- 灵活的配置文件管理
- 支持多协议配置（RS485等）
- 可配置的锅炉参数设置

## 技术栈

- **开发框架**: Qt 6.9.1
- **编程语言**: C++17
- **界面技术**: QML + Qt Quick Controls 2
- **图表组件**: Qt Charts
- **多媒体**: Qt Multimedia
- **网络通信**: Qt Network
- **串口通信**: RS485协议
- **数据存储**: CSV文件格式
- **构建系统**: qmake

## 项目结构

```
zhiHuiRanShao/
├── 核心模块
│   ├── main.cpp                    # 程序入口，初始化各模块
│   ├── config.ini                  # 系统配置文件
│   ├── config_manager.cpp/.h       # 配置文件解析器
│   ├── smoke_analyzer_comm.cpp/.h  # 串口数据采集模块
│   ├── boiler.cpp/.h              # 锅炉线程管理器
│   ├── csvfile.cpp/.h             # CSV文件写入模块
│   └── csvreader.cpp/.h           # CSV文件读取模块
├── 界面模块
│   ├── main.qml                   # 主界面入口
│   ├── MonitoringSystem.qml       # 锅炉监控主界面
│   ├── DataScreenView.qml         # 数据大屏界面
│   ├── VideoView.qml              # 视频监控界面
│   ├── CalendarDataView.qml       # 历史数据查看界面
│   └── ParameterAdjustmentView.qml # 参数调整界面
├── 数据源模块
│   ├── monitoring_datasource.cpp/.h # 前端数据源管理
│   ├── datascreen.cpp/.h          # 数据大屏数据源
│   └── monitorwindow.cpp/.h       # 监控窗口管理
├── 多媒体模块
│   ├── videoplayer.cpp/.h         # 视频播放器
│   ├── videomanager.cpp/.h        # 视频管理器
│   └── video/                     # 视频文件目录
├── 模型管理
│   ├── modelmanager.cpp/.h        # 模型管理器
│   └── widget.cpp/.h              # 主窗口组件
├── 资源文件
│   ├── images/                    # 图片资源
│   ├── data/                      # 数据存储目录
│   └── qml.qrc                    # QML资源文件
└── 项目配置
    ├── SmartBurning.pro           # Qt项目文件
    ├── SmartBurning_zh_CN.ts      # 中文翻译文件
    └── *.ui                       # UI界面文件
```

## 核心模块功能详解

### 配置管理模块 (config_manager.cpp)
- **功能**: 解析和管理config.ini配置文件
- **特性**: 
  - 支持INI格式配置文件读写
  - 线程安全的配置访问
  - 模板化的类型转换
  - 支持配置项的增删改查

### 数据采集模块 (smoke_analyzer_comm.cpp)
- **功能**: 通过RS485串口采集设备数据
- **特性**:
  - 支持多种串口参数配置
  - 异步数据采集
  - 数据校验和错误处理
  - 实时数据缓存

### 锅炉管理模块 (boiler.cpp)
- **功能**: 管理锅炉实例和数据采集线程
- **特性**:
  - 多锅炉并发管理
  - 独立的数据采集线程
  - 可配置的采集间隔
  - 自动CSV数据记录

### CSV文件处理模块
- **csvfile.cpp**: CSV文件写入，支持缓冲写入和定时刷新
- **csvreader.cpp**: CSV文件读取，支持按日期查询历史数据

### 数据源模块 (monitoring_datasource.cpp)
- **功能**: 为QML界面提供实时数据
- **特性**:
  - Qt属性绑定系统
  - 实时数据更新通知
  - 多锅炉数据源切换
  - 图表数据格式化

## 安装和运行指南

### 系统要求
- **操作系统**: Windows 10/11 或 Linux
- **Qt版本**: Qt 6.9.1 或更高版本
- **编译器**: 
  - Windows: MinGW 64-bit 或 MSVC
  - Linux: GCC 支持C++17
- **硬件**: 支持RS485通信的串口设备

### 编译步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd zhiHuiRanShao
```

2. **配置Qt环境**
确保Qt 6.9.1已正确安装并配置环境变量

3. **编译项目**
```bash
qmake SmartBurning.pro
make
```

4. **运行程序**
```bash
# Windows
./release/SmartBurning.exe

# Linux
./SmartBurning
```

### 配置文件设置

编辑 `config.ini` 文件配置系统参数：

```ini
[ProtocolList]
list=RS485,RS485_1

[RS485]
Port = COM5
BaudRate = 9600
StopBits = 2
Parity = N
DataBits = 8
Timeout = 1.0

[BoilerList]
list=Boiler1

[Boiler1]
Desc = this is aboiler1
Protocol = RS485
CollectionInterval = 15
Nox = 30
Current = 60
So2 = 30
Voltage = 50
O2 = 4
Co = 1
Tempature = 40
```

## 使用说明

### 启动系统
1. 确保串口设备正确连接
2. 检查config.ini配置文件
3. 启动SmartBurning程序
4. 系统将自动加载配置并开始数据采集

### 主要操作界面

#### 1. 主页面 (HomePage.qml)
- 系统导航入口
- 功能模块选择

#### 2. 监控系统 (MonitoringSystem.qml)
- 实时数据显示
- 多参数图表监控
- 锅炉状态指示

#### 3. 数据大屏 (DataScreenView.qml)
- 全屏数据展示
- 多维度数据可视化

#### 4. 历史数据查看 (CalendarDataView.qml)
- 按日期查询历史数据
- 数据导出功能
- 趋势分析

### 数据文件管理
- **存储位置**: `data/` 目录
- **文件格式**: `{锅炉名}-{YYYYMMDD}.csv`
- **数据字段**: TimeStamp,O2,CO,NOx,SO2,T,V,I

## 开发和扩展

### 添加新的锅炉类型
1. 在config.ini中添加新的锅炉配置
2. 根据需要扩展Boiler类
3. 更新数据采集逻辑

### 自定义界面组件
1. 在QML中创建新组件
2. 注册到main.cpp的QML上下文
3. 添加到主导航系统

### 扩展数据源
1. 继承MonitoringDataSource类
2. 实现新的数据采集接口
3. 更新QML数据绑定

## 故障排除

### 常见问题
1. **串口连接失败**: 检查串口号和参数配置
2. **配置文件加载失败**: 确认config.ini文件存在且格式正确
3. **数据采集异常**: 检查设备连接和通信协议
4. **界面显示异常**: 确认QML资源文件完整

### 调试模式
编译时启用DEBUG模式可获得详细的调试信息：
```bash
qmake CONFIG+=debug SmartBurning.pro
make
```

## 许可证

本项目采用 [许可证类型] 许可证。详情请参阅 LICENSE 文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 联系信息

如有问题或建议，请联系项目维护者。

---

**版本**: 1.0.0  
**最后更新**: 2025-06-19
