import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
import QtCharts

Window {
    id: root
    width: 1920
    height: 1080
    visible: true
    title: "华润电力湖北有限公司锅炉智慧燃烧系统"

    // 处理窗口关闭事件
    onClosing: function(close) {
        close.accepted = false  // 阻止默认关闭行为
        exitConfirmDialog.open()  // 显示退出确认对话框
    }

    // 连接模型管理器信号
    Connections {
        target: modelManager
        function onModelSwitchCompleted(success, message) {
            if (success) {
                stackView.push(completionPage, {message: message})
            } else {
                // 显示错误消息
                errorDialog.text = message
                errorDialog.open()
            }
        }
    }

    // 错误对话框
    Dialog {
        id: errorDialog
        property alias text: errorLabel.text

        anchors.centerIn: parent
        width: 300
        height: 150
        title: "错误"

        Label {
            id: errorLabel
            anchors.centerIn: parent
            wrapMode: Text.WordWrap
            width: parent.width - 40
        }

        standardButtons: Dialog.Ok
    }

    // 退出确认对话框
    Dialog {
        id: exitConfirmDialog
        anchors.centerIn: parent
        width: 480
        height: 280
        modal: true

        // 移除默认标题栏，使用自定义样式
        header: null

        // 自定义背景
        background: Rectangle {
            color: "#f8f9fa"
            radius: 12
            border.color: "#e9ecef"
            border.width: 1

            // 添加阴影效果
            Rectangle {
                anchors.fill: parent
                anchors.margins: -2
                color: "transparent"
                radius: parent.radius + 2
                border.color: "#00000020"
                border.width: 2
                z: -1
            }
        }

        // 对话框内容
        contentItem: Rectangle {
            color: "transparent"

            Column {
                anchors.centerIn: parent
                spacing: 30
                width: parent.width - 60

                // 标题区域
                Rectangle {
                    width: parent.width
                    height: 60
                    color: "transparent"

                    Row {
                        anchors.centerIn: parent
                        spacing: 15

                        Text {
                            text: "⚠️"
                            font.pixelSize: 32
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Text {
                            text: "确认退出"
                            font.pixelSize: 24
                            font.bold: true
                            color: "#343a40"
                            anchors.verticalCenter: parent.verticalCenter
                        }
                    }
                }

                // 提示信息
                Text {
                    text: "您确定要退出华润电力湖北有限公司锅炉智慧燃烧系统吗？\n您可以选择最小化到系统托盘继续后台运行。"
                    font.pixelSize: 16
                    color: "#6c757d"
                    horizontalAlignment: Text.AlignHCenter
                    wrapMode: Text.WordWrap
                    width: parent.width
                    lineHeight: 1.4
                }

                // 按钮区域
                Row {
                    anchors.horizontalCenter: parent.horizontalCenter
                    spacing: 20

                    // 最小化到托盘按钮
                    Button {
                        width: 160
                        height: 45

                        background: Rectangle {
                            color: parent.pressed ? "#0056b3" : (parent.hovered ? "#0069d9" : "#007bff")
                            radius: 8
                            border.color: "#007bff"
                            border.width: 1

                            // 渐变效果
                            gradient: Gradient {
                                GradientStop { position: 0.0; color: parent.parent.pressed ? "#0056b3" : (parent.parent.hovered ? "#0069d9" : "#007bff") }
                                GradientStop { position: 1.0; color: parent.parent.pressed ? "#004085" : (parent.parent.hovered ? "#0056b3" : "#0069d9") }
                            }
                        }

                        contentItem: Row {
                            anchors.centerIn: parent
                            spacing: 8

                            Text {
                                text: "📥"
                                font.pixelSize: 16
                                color: "white"
                                anchors.verticalCenter: parent.verticalCenter
                            }

                            Text {
                                text: "最小化到托盘"
                                font.pixelSize: 14
                                font.bold: true
                                color: "white"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        onClicked: {
                            exitConfirmDialog.close()
                            if (systemTray.available) {
                                systemTray.hideToTray()
                            } else {
                                root.hide()
                            }
                        }
                    }

                    // 退出程序按钮
                    Button {
                        width: 120
                        height: 45

                        background: Rectangle {
                            color: parent.pressed ? "#c82333" : (parent.hovered ? "#e21e37" : "#dc3545")
                            radius: 8
                            border.color: "#dc3545"
                            border.width: 1

                            // 渐变效果
                            gradient: Gradient {
                                GradientStop { position: 0.0; color: parent.parent.pressed ? "#c82333" : (parent.parent.hovered ? "#e21e37" : "#dc3545") }
                                GradientStop { position: 1.0; color: parent.parent.pressed ? "#a71e2a" : (parent.parent.hovered ? "#c82333" : "#c82333") }
                            }
                        }

                        contentItem: Row {
                            anchors.centerIn: parent
                            spacing: 8

                            Text {
                                text: "🚪"
                                font.pixelSize: 16
                                color: "white"
                                anchors.verticalCenter: parent.verticalCenter
                            }

                            Text {
                                text: "退出程序"
                                font.pixelSize: 14
                                font.bold: true
                                color: "white"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        onClicked: {
                            exitConfirmDialog.close()
                            systemTray.quitApplication()
                        }
                    }


                }
            }
        }
    }

    StackView {
        id: stackView
        anchors.fill: parent
        initialItem: homePage
    }

    // 首页
    Component {
        id: homePage
        HomePage {
            onNavigateToModelList: stackView.push(modelListPage)
            onNavigateToMonitoring: stackView.push(monitorPage)
            onNavigateToDataScreen: stackView.push(dataScreenPage)
            onNavigateToVideo: stackView.push(videoPage)
            onNavigateToParameterAdjustment: stackView.push(parameterAdjustmentPage)
        }
    }

    // 模型列表页面
    Component {
        id: modelListPage
        ModelManagement {
            onNavigateBack: stackView.pop()
            onNavigateToSelection: stackView.push(modelSelectionPage)
            onNavigateToPassword: function(targetModel) {
                stackView.push(passwordPage, {targetModel: targetModel})
            }
            onNavigateToSecondPassword: function(targetModel) {
                stackView.push(secondPasswordPage, {targetModel: targetModel})
            }
            onNavigateToConfirmation: function(targetModel) {
                stackView.push(confirmationPage, {targetModel: targetModel})
            }
            onNavigateToLoading: function(targetModel) {
                stackView.push(loadingPage, {targetModel: targetModel})
            }
        }
    }

    // 模型选择页面
    Component {
        id: modelSelectionPage
        ModelSelection {
            onNavigateBack: stackView.pop()
            onNavigateToPassword: function(targetModel) {
                stackView.push(passwordPage, {targetModel: targetModel})
            }
        }
    }

    // 密码输入页面
    Component {
        id: passwordPage
        PasswordInput {
            onNavigateBack: stackView.pop()
            onNavigateToSecondPassword: function(targetModel) {
                stackView.push(secondPasswordPage, {targetModel: targetModel})
            }
        }
    }

    // 第二人密码输入页面
    Component {
        id: secondPasswordPage
        SecondPasswordInput {
            onNavigateBack: stackView.pop()
            onNavigateToConfirmation: function(targetModel) {
                stackView.push(confirmationPage, {targetModel: targetModel})
            }
        }
    }

    // 最终确认页面
    Component {
        id: confirmationPage
        ModelConfirmation {
            onNavigateBack: stackView.pop()
            onNavigateToLoading: function(targetModel) {
                stackView.push(loadingPage, {targetModel: targetModel})
            }
        }
    }

    // 加载页面
    Component {
        id: loadingPage
        ModelLoading {}
    }

    // 完成页面
    Component {
        id: completionPage
        ModelCompletion {
            onNavigateToHome: stackView.pop(null)
        }
    }

    // 数据监控页面
    Component {
        id: monitorPage
        MonitoringSystem {
            onNavigateBack: {
                monitorWindow.stopMonitoring()
                stackView.pop()
            }
        }
    }

    // 采集配置页面
    Component {
        id: configPage
        CollectionConfigView {
            onNavigateBack: stackView.pop()
        }
    }



    // 多维智慧燃烧控制系统页面
    Component {
        id: dataScreenPage
        DataScreenView {}
    }

    // 视频监控页面
    Component {
        id: videoPage
        VideoView {}
    }

    // 参数调整页面
    Component {
        id: parameterAdjustmentPage
        ParameterAdjustmentView {}
    }


}
