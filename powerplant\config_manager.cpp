#include "config_manager.h"
#include <mutex>

// 去除字符串两端空白
std::string ConfigManager::trim(const std::string& str) {
    const std::string whitespace = " \t\r\n";
    size_t start = str.find_first_not_of(whitespace);
    if (start == std::string::npos) return "";

    size_t end = str.find_last_not_of(whitespace);
    return str.substr(start, end - start + 1);
}

// 解析配置行
bool ConfigManager::parse_line(const std::string& line, std::string& section, std::string& key, std::string& value) {
    std::string trimmed = trim(line);

    // 空行或注释
    if (trimmed.empty() || trimmed[0] == ';' || trimmed[0] == '#') {
        return false;
    }

    // 节标记
    if (trimmed[0] == '[' && trimmed.back() == ']') {
        section = trim(trimmed.substr(1, trimmed.length() - 2));
        key.clear();
        value.clear();
        return true;
    }

    // 键值对
    size_t eq_pos = trimmed.find('=');
    if (eq_pos != std::string::npos) {
        key = trim(trimmed.substr(0, eq_pos));
        value = trim(trimmed.substr(eq_pos + 1));
        return true;
    }

    return false;
}

// 构造函数
ConfigManager::ConfigManager(const std::string& path) : file_path(path) {
    // 确保目录存在
    size_t last_slash = file_path.find_last_of("/\\");
    if (last_slash != std::string::npos) {
        std::string dir = file_path.substr(0, last_slash);
        if (!dir.empty()) {
#ifdef _WIN32
            _mkdir(dir.c_str());
#else
            mkdir(dir.c_str(), 0755);
#endif
        }
    }
}

// 读取配置文件
bool ConfigManager::load() {
    std::lock_guard<std::mutex> lock(rw_mutex);

    std::ifstream file(file_path);
    if (!file.is_open()) {
        std::cerr << "Failed to open config file: " << file_path << std::endl;
        return false;
    }

    // 清空现有数据
    config_data.clear();

    std::string line;
    std::string current_section;

    while (std::getline(file, line)) {
        std::string section, key, value;
        if (parse_line(line, section, key, value)) {
            if (!section.empty()) {
                // 新的节
                current_section = section;
            } else if (!current_section.empty() && !key.empty()) {
                // 键值对
                config_data[current_section][key] = value;
            }
        }
    }

    file.close();
    return true;
}

// 保存配置文件
bool ConfigManager::save() {
    std::lock_guard<std::mutex> lock(rw_mutex);

    std::ofstream file(file_path);
    if (!file.is_open()) {
        std::cerr << "Failed to open config file for writing: " << file_path << std::endl;
        return false;
    }

    // 添加文件头注释
    file << "; Configuration file for RS485 application\n";
    file << "; Generated at " << __DATE__ << " " << __TIME__ << "\n\n";

    // 写入所有配置数据
    for (const auto& section_pair : config_data) {
        const std::string& section = section_pair.first;
        file << "[" << section << "]\n";

        for (const auto& key_value_pair : section_pair.second) {
            const std::string& key = key_value_pair.first;
            const std::string& value = key_value_pair.second;
            file << key << " = " << value << "\n";
        }

        file << "\n";
    }

    file.close();
    return true;
}

// 检查配置项是否存在
bool ConfigManager::exists(const std::string& section, const std::string& key) const {
    std::lock_guard<std::mutex> lock(rw_mutex);

    auto sec_it = config_data.find(section);
    if (sec_it == config_data.end()) {
        return false;
    }

    return sec_it->second.find(key) != sec_it->second.end();
}

// 删除配置项
bool ConfigManager::remove(const std::string& section, const std::string& key) {
    std::lock_guard<std::mutex> lock(rw_mutex);

    auto sec_it = config_data.find(section);
    if (sec_it == config_data.end()) {
        return false;
    }

    auto key_it = sec_it->second.find(key);
    if (key_it == sec_it->second.end()) {
        return false;
    }

    sec_it->second.erase(key_it);

    // 如果节为空，删除整个节
    if (sec_it->second.empty()) {
        config_data.erase(sec_it);
    }

    return true;
}

// 删除整个节
bool ConfigManager::remove_section(const std::string& section) {
    std::lock_guard<std::mutex> lock(rw_mutex);

    auto sec_it = config_data.find(section);
    if (sec_it == config_data.end()) {
        return false;
    }

    config_data.erase(sec_it);
    return true;
}

// 获取所有节名
std::vector<std::string> ConfigManager::get_sections() const {
    std::lock_guard<std::mutex> lock(rw_mutex);

    std::vector<std::string> sections;
    sections.reserve(config_data.size());

    for (const auto& pair : config_data) {
        sections.push_back(pair.first);
    }

    return sections;
}

// 获取指定节中的所有键
std::vector<std::string> ConfigManager::get_keys(const std::string& section) const {
    std::lock_guard<std::mutex> lock(rw_mutex);

    auto sec_it = config_data.find(section);
    if (sec_it == config_data.end()) {
        return {};
    }

    std::vector<std::string> keys;
    keys.reserve(sec_it->second.size());

    for (const auto& pair : sec_it->second) {
        keys.push_back(pair.first);
    }

    return keys;
}

// 重载流操作符，用于调试
std::ostream& operator<<(std::ostream& os, const ConfigManager& config) {
    std::lock_guard<std::mutex> lock(config.rw_mutex);

    os << "Configuration from " << config.file_path << ":\n";

    for (const auto& section_pair : config.config_data) {
        const std::string& section = section_pair.first;
        os << "[" << section << "]\n";

        for (const auto& key_value_pair : section_pair.second) {
            const std::string& key = key_value_pair.first;
            const std::string& value = key_value_pair.second;
            os << "  " << key << " = " << value << "\n";
        }
    }

    return os;
}
