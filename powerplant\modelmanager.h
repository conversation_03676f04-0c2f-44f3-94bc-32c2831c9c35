#ifndef MODELMANAGER_H
#define MODELMANAGER_H

#include <QObject>
#include <QStringList>
#include <QTimer>

class ModelManager : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QStringList availableModels READ availableModels NOTIFY availableModelsChanged)
    Q_PROPERTY(QString currentModel READ currentModel NOTIFY currentModelChanged)
    Q_PROPERTY(bool isLoading READ isLoading NOTIFY isLoadingChanged)

public:
    explicit ModelManager(QObject *parent = nullptr);

    QStringList availableModels() const;
    QString currentModel() const;
    bool isLoading() const;

    Q_INVOKABLE bool verifyPassword(const QString &password);
    Q_INVOKABLE bool verifySecondPassword(const QString &password);
    Q_INVOKABLE void switchModel(const QString &modelName);
    Q_INVOKABLE QString getModelDescription(const QString &modelName);

signals:
    void availableModelsChanged();
    void currentModelChanged();
    void isLoadingChanged();
    void modelSwitchCompleted(bool success, const QString &message);

private slots:
    void onModelLoadingFinished();

private:
    QStringList m_availableModels;
    QString m_currentModel;
    bool m_isLoading;
    QTimer *m_loadingTimer;
    QString m_targetModel;
};

#endif // MODELMANAGER_H