# Qt-specific ignores
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
*.moc
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
Makefile*
*build-*
*.qm
*.prl

# Build directories
build/
debug/
release/
bin/
lib/

# Qt Creator autogenerated files
CMakeLists.txt.user*

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# OS-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor backup files
*~
*.autosave
*.swp
*.bak

# Qt Creator local machine files
*.stash
.qmake.cache
.qmake.stash

# Generated files by Qt Creator
*.qmlc
*.jsc
*.qmlproject.user
*.qmlproject.user.*

# Visual Studio specific
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
.vs/

# IDE specific files
.idea/
.qtcreator/

# Generated UI files
ui_*.h

# Runtime data files
data/*.csv
data/*.txt
data/*.log

# Configuration cache
.qmake.stash

# Deployment scripts (if auto-generated)
# deploy_compatible_v2.bat

# Large media files (optional - uncomment if you don't want to track video files)
# video/*.mp4
# video/*.avi
# video/*.mov

# Documentation files (optional - uncomment if you don't want to track docs)
# *.docx
# *.doc
# *.pdf