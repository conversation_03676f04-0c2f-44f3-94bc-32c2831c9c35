<RCC>
    <qresource prefix="/">
        <file>main.qml</file>
        <file>HomePage.qml</file>
        <file>MonitoringSystem.qml</file>
        <file>DataScreenView.qml</file>
        <file>ParameterAdjustmentView.qml</file>

        <file>CollectionConfigView.qml</file>
    </qresource>
    <qresource prefix="/images">
        <file alias="app-icon.ico">images/app-icon.ico</file>
        <file alias="app-icon.png">images/app-icon.ico</file>
        <file alias="backhome.png">images/backhome.png</file>
        <file alias="background.png">images/background.png</file>

        <!-- DataScreen images -->
        <file alias="top-bg.png">images/top-bg.png</file>
        <file alias="title-bg.png">images/title-bg.png</file>
        <file alias="left-frame.png">images/left-frame.png</file>
        <file alias="right-frame.png">images/right-frame.png</file>
        <file alias="main-diagram.png">images/main-diagram.png</file>
        <file alias="temperature-icon.png">images/temperature-icon.png</file>
        <file alias="pressure-icon.png">images/pressure-icon.png</file>
        <file alias="oxygen-icon.png">images/oxygen-icon.png</file>
        <file alias="co-icon.png">images/co-icon.png</file>
        <file alias="fan-icon.png">images/fan-icon.png</file>

    </qresource>
    <qresource prefix="/data">
        <!-- CSV数据文件将在运行时动态创建，这里预留资源路径 -->
    </qresource>
</RCC>
