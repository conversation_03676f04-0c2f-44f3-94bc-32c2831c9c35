import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: videoPage

    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    // 连接VideoManager信号
    Connections {
        target: videoManager
        function onVideoStateChanged(isPlaying) {
            statusLabel.text = isPlaying ? "播放中" : "已暂停"
        }
        function onVideoError(error) {
            errorDialog.text = error
            errorDialog.open()
        }
    }

    // 错误对话框
    Dialog {
        id: errorDialog
        property alias text: errorLabel.text

        anchors.centerIn: parent
        width: 300
        height: 150
        title: "错误"

        Label {
            id: errorLabel
            anchors.centerIn: parent
            wrapMode: Text.WordWrap
            width: parent.width - 40
        }

        standardButtons: Dialog.Ok
    }

    // 锅炉选择按钮组
    ButtonGroup {
        id: boilerButtonGroup
        exclusive: true
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回首页"
                onClicked: stackView.pop()
            }
            Label {
                text: "锅炉火焰图像识别系统"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }
        }
    }

    // 主要内容区域
    Rectangle {
        anchors.fill: parent
        anchors.margins: 20
        color: "transparent"
        
        ColumnLayout {
            anchors.fill: parent
            spacing: 20
            
            // 标题区域
            Rectangle {
                Layout.fillWidth: true
                height: 80
                color: "#f0f8ff"
                radius: 12
                border.color: "#2196f3"
                border.width: 2

                RowLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 30

                    Rectangle {
                        width: 16
                        height: 16
                        radius: 8
                        color: "#4caf50"
                    }

                    Label {
                        text: "锅炉火焰图像识别系统"
                        font.pixelSize: 24
                        font.bold: true
                        color: "#333333"
                        Layout.fillWidth: true
                    }

                    Label {
                        text: "实时视频监控"
                        font.pixelSize: 16
                        color: "#666666"
                    }
                }
            }
            
            // 视频控制区域
            Rectangle {
                Layout.fillWidth: true
                Layout.fillHeight: true
                color: "#ffffff"
                radius: 12
                border.color: "#e0e0e0"
                border.width: 1
                
                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 15
                    
                    Label {
                        text: "锅炉视频监控"
                        font.pixelSize: 20
                        font.bold: true
                        color: "#333333"
                        Layout.alignment: Qt.AlignHCenter
                    }
                    
                    // 锅炉选择按钮区域
                    Rectangle {
                        Layout.fillWidth: true
                        height: 80
                        color: "#f5f5f5"
                        radius: 8
                        border.color: "#ddd"
                        border.width: 1
                        
                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 20
                            
                            Label {
                                text: "选择锅炉:"
                                font.pixelSize: 16
                                font.bold: true
                                color: "#333333"
                            }
                            
                            Button {
                                text: "1#锅炉"
                                Layout.preferredWidth: 100
                                Layout.preferredHeight: 40
                                checkable: true
                                checked: true
                                ButtonGroup.group: boilerButtonGroup
                                
                                background: Rectangle {
                                    color: parent.checked ? "#E3F2FD" : "#ffffff"
                                    border.color: parent.checked ? "#2196f3" : "#ddd"
                                    border.width: 2
                                    radius: 8
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: parent.checked ? "#2196f3" : "#333333"
                                    font.pixelSize: 14
                                    font.bold: parent.checked
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: {
                                    videoManager.playVideo(1)
                                    console.log("选择了1#锅炉")
                                }
                            }
                            
                            Button {
                                text: "2#锅炉"
                                Layout.preferredWidth: 100
                                Layout.preferredHeight: 40
                                checkable: true
                                ButtonGroup.group: boilerButtonGroup
                                
                                background: Rectangle {
                                    color: parent.checked ? "#E3F2FD" : "#ffffff"
                                    border.color: parent.checked ? "#2196f3" : "#ddd"
                                    border.width: 2
                                    radius: 8
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: parent.checked ? "#2196f3" : "#333333"
                                    font.pixelSize: 14
                                    font.bold: parent.checked
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: {
                                    videoManager.playVideo(2)
                                    console.log("选择了2#锅炉")
                                }
                            }
                            
                            Button {
                                text: "3#锅炉"
                                Layout.preferredWidth: 100
                                Layout.preferredHeight: 40
                                checkable: true
                                ButtonGroup.group: boilerButtonGroup
                                
                                background: Rectangle {
                                    color: parent.checked ? "#E3F2FD" : "#ffffff"
                                    border.color: parent.checked ? "#2196f3" : "#ddd"
                                    border.width: 2
                                    radius: 8
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: parent.checked ? "#2196f3" : "#333333"
                                    font.pixelSize: 14
                                    font.bold: parent.checked
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: {
                                    videoManager.playVideo(3)
                                    console.log("选择了3#锅炉")
                                }
                            }
                            
                            Button {
                                text: "4#锅炉"
                                Layout.preferredWidth: 100
                                Layout.preferredHeight: 40
                                checkable: true
                                ButtonGroup.group: boilerButtonGroup
                                
                                background: Rectangle {
                                    color: parent.checked ? "#E3F2FD" : "#ffffff"
                                    border.color: parent.checked ? "#2196f3" : "#ddd"
                                    border.width: 2
                                    radius: 8
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    color: parent.checked ? "#2196f3" : "#333333"
                                    font.pixelSize: 14
                                    font.bold: parent.checked
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                onClicked: {
                                    videoManager.playVideo(4)
                                    console.log("选择了4#锅炉")
                                }
                            }
                            
                            Item {
                                Layout.fillWidth: true
                            }
                        }
                    }
                    
                    // 内嵌视频播放器
                    VideoPlayerWidget {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                    }
                }
            }
        }
    }

    // 页面加载完成后的初始化
    Component.onCompleted: {
        console.log("VideoView loaded successfully")
    }
}
