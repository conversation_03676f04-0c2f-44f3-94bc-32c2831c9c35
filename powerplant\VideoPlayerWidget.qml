import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: root
    color: "#000000"
    border.color: "#333333"
    border.width: 2
    radius: 8

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 5
        spacing: 5

        // 视频显示区域占位符
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#000000"
            radius: 4
            border.color: "#333333"
            border.width: 1

            ColumnLayout {
                anchors.centerIn: parent
                spacing: 20

                Rectangle {
                    Layout.alignment: Qt.AlignHCenter
                    width: 100
                    height: 100
                    color: "#333333"
                    radius: 50

                    Text {
                        anchors.centerIn: parent
                        text: videoManager.isPlaying ? "📺" : "▶"
                        font.pixelSize: 40
                        color: "#ffffff"
                    }
                }

                Label {
                    text: videoManager.isPlaying ?
                          ("正在播放 " + videoManager.currentBoiler + "#锅炉视频") :
                          "请选择锅炉开始播放"
                    font.pixelSize: 18
                    color: "#ffffff"
                    Layout.alignment: Qt.AlignHCenter
                }

                Label {
                    text: "视频将在独立窗口中播放"
                    font.pixelSize: 14
                    color: "#cccccc"
                    Layout.alignment: Qt.AlignHCenter
                }
            }
        }
        
        // 控制栏
        Rectangle {
            Layout.fillWidth: true
            height: 60
            color: "#1a1a1a"
            radius: 4
            border.color: "#333333"
            border.width: 1

            RowLayout {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 15

                // 播放/暂停按钮
                Button {
                    text: videoManager.isPlaying ? "⏸" : "▶"
                    Layout.preferredWidth: 50
                    Layout.preferredHeight: 40
                    enabled: videoManager.currentBoiler > 0

                    background: Rectangle {
                        color: parent.enabled ? (parent.pressed ? "#1976d2" : "#2196f3") : "#666666"
                        radius: 20
                    }

                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 16
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }

                    onClicked: {
                        videoManager.togglePlayPause()
                    }
                }

                // 停止按钮
                Button {
                    text: "⏹"
                    Layout.preferredWidth: 50
                    Layout.preferredHeight: 40
                    enabled: videoManager.isPlaying

                    background: Rectangle {
                        color: parent.enabled ? (parent.pressed ? "#d32f2f" : "#f44336") : "#666666"
                        radius: 20
                    }

                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 16
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }

                    onClicked: {
                        videoManager.stopVideo()
                    }
                }

                // 显示视频窗口按钮
                Button {
                    text: "🖥"
                    Layout.preferredWidth: 50
                    Layout.preferredHeight: 40
                    enabled: videoManager.isPlaying

                    background: Rectangle {
                        color: parent.enabled ? (parent.pressed ? "#388e3c" : "#4caf50") : "#666666"
                        radius: 20
                    }

                    contentItem: Text {
                        text: parent.text
                        color: "white"
                        font.pixelSize: 16
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }

                    onClicked: {
                        videoManager.showVideoWindow()
                    }
                }

                Item {
                    Layout.fillWidth: true
                }

                // 状态显示
                Label {
                    text: videoManager.isPlaying ?
                          ("播放中: " + videoManager.currentBoiler + "#锅炉") :
                          "就绪"
                    font.pixelSize: 14
                    color: videoManager.isPlaying ? "#4caf50" : "#cccccc"
                }
            }
        }
    }
}
