import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: modelLoading
    
    property string targetModel: ""
    
    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Label {
                text: "模型切换中"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }
        }
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 30

        Item {
            Layout.fillHeight: true
        }

        // 加载动画
        Rectangle {
            Layout.alignment: Qt.AlignHCenter
            width: 150
            height: 150
            color: "transparent"

            Rectangle {
                id: loadingIndicator
                width: 120
                height: 120
                anchors.centerIn: parent
                color: "transparent"
                border.color: "#2196f3"
                border.width: 6
                radius: 60

                Rectangle {
                    width: 30
                    height: 30
                    radius: 15
                    color: "#2196f3"
                    anchors.top: parent.top
                    anchors.horizontalCenter: parent.horizontalCenter
                    anchors.topMargin: -15
                }

                RotationAnimation {
                    target: loadingIndicator
                    property: "rotation"
                    from: 0
                    to: 360
                    duration: 1000
                    loops: Animation.Infinite
                    running: true
                }
            }
        }

        Label {
            text: "正在切换到: " + targetModel
            font.pixelSize: 28
            font.bold: true
            Layout.alignment: Qt.AlignHCenter
            color: "#ffffff"
        }

        Label {
            text: "模型正在加载中，请稍候..."
            font.pixelSize: 20
            color: "#cccccc"
            Layout.alignment: Qt.AlignHCenter
        }

        ProgressBar {
            Layout.fillWidth: true
            Layout.maximumWidth: 600
            Layout.alignment: Qt.AlignHCenter
            Layout.preferredHeight: 8
            indeterminate: true
        }

        Label {
            text: "请勿关闭应用程序或进行其他操作"
            font.pixelSize: 16
            color: "#cccccc"
            Layout.alignment: Qt.AlignHCenter
        }

        Item {
            Layout.fillHeight: true
        }
    }
}
