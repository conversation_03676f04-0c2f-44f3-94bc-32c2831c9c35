#include "configmanager_qml.h"
#include <QDebug>
#include <QProcess>
#include <QCoreApplication>
#include <QTimer>
#include <QFile>

ConfigManagerQML::ConfigManagerQML(QObject *parent)
    : QObject(parent), m_configManager(nullptr)
{
}

void ConfigManagerQML::setConfigManager(ConfigManager* manager)
{
    m_configManager = manager;
}

QVariantMap ConfigManagerQML::getRS485Config()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return QVariantMap();
    }

    // 获取RS485通信配置
    QVariantMap rs485Config = sectionToVariantMap("RS485");

    // 获取烟气分析仪设备配置
    std::string boilerList = m_configManager->get<std::string>("BoilerList", "list", "");
    if (!boilerList.empty()) {
        QVariantMap smokeAnalyzerConfig = sectionToVariantMap(boilerList);

        // 合并配置（烟气分析仪设备配置会覆盖RS485配置中的同名项）
        for (auto it = smokeAnalyzerConfig.begin(); it != smokeAnalyzerConfig.end(); ++it) {
            rs485Config[it.key()] = it.value();
        }
    }

    return rs485Config;
}

QVariantMap ConfigManagerQML::getDCSRS485Config()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return QVariantMap();
    }

    return sectionToVariantMap("DCS_RS485");
}

QVariantMap ConfigManagerQML::getDCSOPCConfig()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return QVariantMap();
    }

    return sectionToVariantMap("DCS1");
}

QVariantMap ConfigManagerQML::getParameterAdjustmentConfig()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return QVariantMap();
    }

    return sectionToVariantMap("ParameterAdjustment");
}

bool ConfigManagerQML::saveRS485Config(const QVariantMap& config)
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }
    
    bool success = variantMapToSection("RS485", config);
    if (success) {
        success = m_configManager->save();
        emit configSaved(success);
        if (success) {
            emit configChanged();
        }
    }
    return success;
}

bool ConfigManagerQML::saveDCSRS485Config(const QVariantMap& config)
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }

    bool success = variantMapToSection("DCS_RS485", config);
    if (success) {
        success = m_configManager->save();
        emit configSaved(success);
        if (success) {
            emit configChanged();
        }
    }
    return success;
}

bool ConfigManagerQML::saveDCSOPCConfig(const QVariantMap& config)
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }

    bool success = variantMapToSection("DCS1", config);
    if (success) {
        success = m_configManager->save();
        emit configSaved(success);
        if (success) {
            emit configChanged();
        }
    }
    return success;
}

bool ConfigManagerQML::saveParameterAdjustmentConfig(const QVariantMap& config)
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }

    bool success = variantMapToSection("ParameterAdjustment", config);
    if (success) {
        success = m_configManager->save();
        emit configSaved(success);
        if (success) {
            emit configChanged();
        }
    }
    return success;
}

bool ConfigManagerQML::saveAllConfigs()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }
    
    bool success = m_configManager->save();
    emit configSaved(success);
    if (success) {
        emit configChanged();
    }
    return success;
}

bool ConfigManagerQML::reloadConfigs()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }
    
    bool success = m_configManager->load();
    emit configReloaded(success);
    if (success) {
        emit configChanged();
    }
    return success;
}



bool ConfigManagerQML::restartApplication()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }

    try {
        qDebug() << "准备重启应用程序...";

        // 1. 先保存所有配置
        bool configSaved = m_configManager->save();
        if (!configSaved) {
            qWarning() << "配置保存失败，取消重启";
            return false;
        }
        qDebug() << "配置保存成功";

        // 2. 获取重启脚本路径和工作目录
        QString workingDir = QCoreApplication::applicationDirPath();
        QString restartBatPath = workingDir + "/重启软件.bat";

        qDebug() << "重启脚本路径:" << restartBatPath;
        qDebug() << "工作目录:" << workingDir;

        // 3. 检查重启脚本是否存在
        if (!QFile::exists(restartBatPath)) {
            qWarning() << "重启脚本不存在:" << restartBatPath;
            return false;
        }

        // 4. 使用重启脚本启动新的应用程序实例
        bool started = QProcess::startDetached(restartBatPath, QStringList(), workingDir);

        if (started) {
            qDebug() << "重启脚本启动成功，应用程序将彻底重启";

            // 5. 延迟退出当前实例，让重启脚本有时间执行
            QTimer::singleShot(1000, [](){
                qDebug() << "退出当前应用程序实例";
                QCoreApplication::quit();
            });

            return true;
        } else {
            qWarning() << "启动重启脚本失败";
            return false;
        }

    } catch (const std::exception& e) {
        qWarning() << "重启应用程序时发生错误:" << e.what();
        return false;
    }
}

void ConfigManagerQML::setConfigValue(const QString& section, const QString& key, const QString& value)
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return;
    }

    m_configManager->set<std::string>(section.toStdString(), key.toStdString(), value.toStdString());
}

QString ConfigManagerQML::getSmokeAnalyzerDeviceName()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return QString();
    }

    // 从BoilerList中获取烟气分析仪设备名称
    std::string boilerList = m_configManager->get<std::string>("BoilerList", "list", "");
    if (!boilerList.empty()) {
        return QString::fromStdString(boilerList);
    }

    return QString();
}

QVariantMap ConfigManagerQML::sectionToVariantMap(const std::string& sectionName)
{
    QVariantMap result;
    
    if (!m_configManager) {
        return result;
    }
    
    // 获取节中的所有键
    std::vector<std::string> keys = m_configManager->get_keys(sectionName);
    
    for (const auto& key : keys) {
        std::string value = m_configManager->get<std::string>(sectionName, key, "");
        result[QString::fromStdString(key)] = QString::fromStdString(value);
    }
    
    return result;
}

bool ConfigManagerQML::variantMapToSection(const std::string& sectionName, const QVariantMap& map)
{
    if (!m_configManager) {
        return false;
    }
    
    try {
        for (auto it = map.begin(); it != map.end(); ++it) {
            std::string key = it.key().toStdString();
            std::string value = it.value().toString().toStdString();
            m_configManager->set<std::string>(sectionName, key, value);
        }
        return true;
    } catch (const std::exception& e) {
        qWarning() << "Error saving config section:" << e.what();
        return false;
    }
}
