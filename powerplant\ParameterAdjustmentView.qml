import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: parameterPage

    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回首页"
                onClicked: stackView.pop()
            }
            Label {
                text: "3#锅炉燃烧参数调整"
                font.pixelSize: 20
                font.bold: true
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }

            // 右上角按钮
            Button {
                text: "手动调整"
                Layout.preferredWidth: 120
                Layout.preferredHeight: 35
                background: Rectangle {
                    color: parent.pressed ? "#1976d2" : "#2196f3"
                    radius: 6
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 14
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                onClicked: {
                    console.log("手动调整模式")
                }
            }

            Button {
                text: "自动调整"
                Layout.preferredWidth: 120
                Layout.preferredHeight: 35
                background: Rectangle {
                    color: parent.pressed ? "#388e3c" : "#4caf50"
                    radius: 6
                }
                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 14
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                onClicked: {
                    console.log("自动调整模式")
                }
            }
        }
    }

    // 主要内容区域
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 40
        spacing: 30

        // 页面标题
        Label {
            text: "3#锅炉燃烧参数调整"
            font.pixelSize: 28
            font.bold: true
            color: "#ffffff"
            Layout.alignment: Qt.AlignHCenter
        }

        // 垂直居中的容器
        Item {
            Layout.fillHeight: true
            Layout.fillWidth: true

            // 居中的表格
            Rectangle {
                anchors.centerIn: parent
                width: Math.min(parent.width * 0.85, 1100)
                height: Math.min(parent.height * 0.95, 900)
                color: "#ffffff"
                radius: 15
                border.color: "#e0e0e0"
                border.width: 2

                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 20
                    spacing: 0

                    // 表头
                    Rectangle {
                        Layout.fillWidth: true
                        height: 50
                        color: "#f5f5f5"
                        radius: 8
                        border.color: "#333333"
                        border.width: 2

                        Row {
                            anchors.fill: parent

                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                border.color: "#333333"
                                border.width: 1
                                color: "transparent"

                                Label {
                                    anchors.centerIn: parent
                                    text: "因素"
                                    font.pixelSize: 16
                                    font.bold: true
                                    color: "#333333"
                                }
                            }

                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                border.color: "#333333"
                                border.width: 1
                                color: "transparent"

                                Label {
                                    anchors.centerIn: parent
                                    text: "现在运行参数"
                                    font.pixelSize: 16
                                    font.bold: true
                                    color: "#333333"
                                }
                            }

                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                border.color: "#333333"
                                border.width: 1
                                color: "transparent"

                                Label {
                                    anchors.centerIn: parent
                                    text: "调整值"
                                    font.pixelSize: 16
                                    font.bold: true
                                    color: "#333333"
                                }
                            }
                        }
                    }

                    // 参数数据列表
                    ListView {
                        Layout.fillWidth: true
                        Layout.fillHeight: true
                        model: parameterModel
                        clip: true

                        delegate: Rectangle {
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? "#ffffff" : "#f9f9f9"
                            border.color: "#333333"
                            border.width: 1

                            Row {
                                anchors.fill: parent

                                Rectangle {
                                    width: parent.width / 3
                                    height: parent.height
                                    border.color: "#333333"
                                    border.width: 1
                                    color: "transparent"

                                    Label {
                                        anchors.centerIn: parent
                                        text: model.factor
                                        font.pixelSize: 14
                                        color: "#333333"
                                        font.bold: true
                                    }
                                }

                                Rectangle {
                                    width: parent.width / 3
                                    height: parent.height
                                    border.color: "#333333"
                                    border.width: 1
                                    color: "transparent"

                                    Label {
                                        anchors.centerIn: parent
                                        text: model.currentValue
                                        font.pixelSize: 14
                                        font.bold: true
                                        color: "#1976d2"
                                    }
                                }

                                Rectangle {
                                    width: parent.width / 3
                                    height: parent.height
                                    border.color: "#333333"
                                    border.width: 1
                                    color: "transparent"

                                    Label {
                                        anchors.centerIn: parent
                                        text: model.suggestedValue
                                        font.pixelSize: 14
                                        font.bold: true
                                        color: "#d32f2f"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 动态数据更新定时器
    Timer {
        id: dataUpdateTimer
        interval: 2000 // 每2秒更新一次
        running: true
        repeat: true
        onTriggered: updateParameterData()
    }

    // 参数数据模型
    ListModel {
        id: parameterModel
    }

    // 数据更新函数
    function updateParameterData() {
        parameterModel.clear()

        // 添加动态变化的数据
        parameterModel.append({
            factor: "O₂浓度",
            currentValue: (3.5 + Math.random() * 0.5).toFixed(3) + "%",
            suggestedValue: "3.5%"
        })

        parameterModel.append({
            factor: "SOFA风",
            currentValue: Math.floor(65 + Math.random() * 10) + "%",
            suggestedValue: "50%"
        })

        parameterModel.append({
            factor: "OFA风",
            currentValue: Math.floor(45 + Math.random() * 10) + "%",
            suggestedValue: "70%"
        })

        parameterModel.append({
            factor: "F磨层风",
            currentValue: Math.floor(65 + Math.random() * 10) + "%",
            suggestedValue: "50%"
        })

        parameterModel.append({
            factor: "E磨层风",
            currentValue: Math.floor(65 + Math.random() * 10) + "%",
            suggestedValue: "50%"
        })

        parameterModel.append({
            factor: "D磨层风",
            currentValue: Math.floor(25 + Math.random() * 10) + "%",
            suggestedValue: "50%"
        })

        parameterModel.append({
            factor: "C磨层风",
            currentValue: Math.floor(25 + Math.random() * 10) + "%",
            suggestedValue: "50%"
        })

        parameterModel.append({
            factor: "B磨层风",
            currentValue: Math.floor(65 + Math.random() * 10) + "%",
            suggestedValue: "30%"
        })

        parameterModel.append({
            factor: "A磨层风",
            currentValue: Math.floor(65 + Math.random() * 10) + "%",
            suggestedValue: "30%"
        })

        parameterModel.append({
            factor: "送风机A风量",
            currentValue: Math.floor(1200 + Math.random() * 200) + "m³/h",
            suggestedValue: "+1%"
        })

        parameterModel.append({
            factor: "送风机B风量",
            currentValue: Math.floor(1200 + Math.random() * 200) + "m³/h",
            suggestedValue: "-2%"
        })

        parameterModel.append({
            factor: "引风机A风量",
            currentValue: Math.floor(1500 + Math.random() * 300) + "m³/h",
            suggestedValue: "+3%"
        })

        parameterModel.append({
            factor: "引风机B风量",
            currentValue: Math.floor(1500 + Math.random() * 300) + "m³/h",
            suggestedValue: "-1%"
        })
    }

    // 页面加载完成后初始化数据
    Component.onCompleted: {
        updateParameterData()
    }
}