import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Page {
    id: modelConfirmation
    
    property string targetModel: ""
    
    // 信号定义
    signal navigateBack()
    signal navigateToLoading(string targetModel)
    
    // 设置深蓝色背景
    background: Rectangle {
        color: "#0E2250"
    }

    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            Button {
                text: "返回"
                onClicked: modelConfirmation.navigateBack()
            }
            Label {
                text: "确认切换模型"
                font.pixelSize: 20
                elide: Label.ElideRight
                horizontalAlignment: Qt.AlignHCenter
                verticalAlignment: Qt.AlignVCenter
                Layout.fillWidth: true
            }
        }
    }
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 60
        spacing: 40

        Item {
            Layout.fillHeight: true
        }

        Label {
            text: "最终确认"
            font.pixelSize: 28
            font.bold: true
            Layout.alignment: Qt.AlignHCenter
            color: "#ffffff"
        }

        Rectangle {
            Layout.fillWidth: true
            Layout.maximumWidth: 700
            Layout.alignment: Qt.AlignHCenter
            height: 120
            color: "#fff3e0"
            radius: 12
            border.color: "#ff9800"
            border.width: 2

            ColumnLayout {
                anchors.centerIn: parent
                width: parent.width - 50
                spacing: 10

                Label {
                    text: "您确定要将当前模型从："
                    font.pixelSize: 18
                    color: "#333333"
                    Layout.alignment: Qt.AlignHCenter
                }

                Label {
                    text: modelManager.currentModel + "  →  " + targetModel
                    font.pixelSize: 22
                    font.bold: true
                    color: "#ff9800"
                    Layout.alignment: Qt.AlignHCenter
                }

                Label {
                    text: "进行切换吗？"
                    font.pixelSize: 18
                    color: "#333333"
                    Layout.alignment: Qt.AlignHCenter
                }
            }
        }

        Label {
            text: "⚠️ 注意：模型切换将会重新加载系统，请确认操作"
            font.pixelSize: 16
            color: "#ffcc80"
            wrapMode: Text.WordWrap
            Layout.fillWidth: true
            Layout.maximumWidth: 600
            Layout.alignment: Qt.AlignHCenter
            horizontalAlignment: Text.AlignHCenter
        }

        Item {
            Layout.fillHeight: true
        }

        RowLayout {
            Layout.alignment: Qt.AlignHCenter
            spacing: 40

            Button {
                text: "取消"
                Layout.preferredWidth: 150
                Layout.preferredHeight: 60
                onClicked: modelConfirmation.navigateBack()

                background: Rectangle {
                    color: parent.pressed ? "#f0f0f0" : "#ffffff"
                    border.color: "#ddd"
                    border.width: 2
                    radius: 8
                }

                contentItem: Text {
                    text: parent.text
                    color: "#333"
                    font.pixelSize: 18
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            Button {
                text: "确认切换"
                Layout.preferredWidth: 150
                Layout.preferredHeight: 60
                onClicked: {
                    modelManager.switchModel(targetModel)
                    // 切换到加载页面
                    modelConfirmation.navigateToLoading(targetModel)
                }

                background: Rectangle {
                    color: parent.pressed ? "#d32f2f" : "#f44336"
                    radius: 8
                }

                contentItem: Text {
                    text: parent.text
                    color: "white"
                    font.pixelSize: 18
                    font.bold: true
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }
    }
}
