import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtCharts 2.15

// 日历数据查看组件示例
Rectangle {
    id: root
    width: 800
    height: 600
    color: "#f0f0f0"
    
    property string selectedBoiler: "Boiler1"
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        
        // 标题
        Text {
            text: "历史数据查看"
            font.pixelSize: 24
            font.bold: true
            Layout.alignment: Qt.AlignHCenter
        }
        
        // 锅炉选择和日期选择
        RowLayout {
            Layout.fillWidth: true
            
            Text {
                text: "选择锅炉:"
                font.pixelSize: 16
            }
            
            ComboBox {
                id: boilerCombo
                model: ["Boiler1", "Boiler2"]
                currentIndex: 0
                onCurrentTextChanged: {
                    root.selectedBoiler = currentText
                    updateAvailableDates()
                }
            }
            
            Item { Layout.fillWidth: true }
            
            Text {
                text: "选择日期:"
                font.pixelSize: 16
            }
            
            ComboBox {
                id: dateCombo
                model: availableDatesModel
                onCurrentTextChanged: {
                    if (currentText) {
                        loadDataForDate(currentText)
                    }
                }
            }
            
            Button {
                text: "刷新"
                onClicked: updateAvailableDates()
            }
        }
        
        // 数据显示区域
        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            ListView {
                id: dataListView
                model: historicalDataModel
                
                delegate: Rectangle {
                    width: dataListView.width
                    height: 40
                    color: index % 2 === 0 ? "#ffffff" : "#f8f8f8"
                    border.color: "#e0e0e0"
                    border.width: 1
                    
                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 5
                        
                        Text {
                            text: model.datetime || ""
                            font.pixelSize: 12
                            Layout.preferredWidth: 150
                        }
                        
                        Text {
                            text: "O₂: " + (model.o2 ? model.o2.toFixed(2) + "%" : "N/A")
                            font.pixelSize: 12
                            Layout.preferredWidth: 80
                        }
                        
                        Text {
                            text: "CO: " + (model.co ? model.co.toFixed(0) + "ppm" : "N/A")
                            font.pixelSize: 12
                            Layout.preferredWidth: 80
                        }
                        
                        Text {
                            text: "NOx: " + (model.nox ? model.nox.toFixed(1) + "mg/m³" : "N/A")
                            font.pixelSize: 12
                            Layout.preferredWidth: 100
                        }
                        
                        Text {
                            text: "SO₂: " + (model.so2 ? model.so2.toFixed(0) + "mg/m³" : "N/A")
                            font.pixelSize: 12
                            Layout.preferredWidth: 100
                        }
                        
                        Text {
                            text: "温度: " + (model.temperature ? model.temperature.toFixed(1) + "°C" : "N/A")
                            font.pixelSize: 12
                            Layout.preferredWidth: 80
                        }
                    }
                }
                
                header: Rectangle {
                    width: dataListView.width
                    height: 40
                    color: "#4a90e2"
                    
                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 5
                        
                        Text {
                            text: "时间"
                            color: "white"
                            font.pixelSize: 14
                            font.bold: true
                            Layout.preferredWidth: 150
                        }
                        
                        Text {
                            text: "氧气"
                            color: "white"
                            font.pixelSize: 14
                            font.bold: true
                            Layout.preferredWidth: 80
                        }
                        
                        Text {
                            text: "一氧化碳"
                            color: "white"
                            font.pixelSize: 14
                            font.bold: true
                            Layout.preferredWidth: 80
                        }
                        
                        Text {
                            text: "氮氧化物"
                            color: "white"
                            font.pixelSize: 14
                            font.bold: true
                            Layout.preferredWidth: 100
                        }
                        
                        Text {
                            text: "二氧化硫"
                            color: "white"
                            font.pixelSize: 14
                            font.bold: true
                            Layout.preferredWidth: 100
                        }
                        
                        Text {
                            text: "温度"
                            color: "white"
                            font.pixelSize: 14
                            font.bold: true
                            Layout.preferredWidth: 80
                        }
                    }
                }
            }
        }
        
        // 状态信息
        Text {
            id: statusText
            text: "请选择日期查看历史数据"
            font.pixelSize: 14
            color: "#666666"
            Layout.alignment: Qt.AlignHCenter
        }
    }
    
    // 数据模型
    ListModel {
        id: availableDatesModel
    }
    
    ListModel {
        id: historicalDataModel
    }
    
    // 函数：更新可用日期列表
    function updateAvailableDates() {
        availableDatesModel.clear()
        
        if (typeof csvReader !== 'undefined') {
            var dates = csvReader.getAvailableDates(root.selectedBoiler)
            for (var i = 0; i < dates.length; i++) {
                availableDatesModel.append({"text": dates[i]})
            }
            
            if (dates.length > 0) {
                dateCombo.currentIndex = dates.length - 1 // 选择最新日期
                statusText.text = "找到 " + dates.length + " 天的历史数据"
            } else {
                statusText.text = "没有找到历史数据"
            }
        } else {
            statusText.text = "CSV读取器未初始化"
        }
    }
    
    // 函数：加载指定日期的数据
    function loadDataForDate(dateString) {
        historicalDataModel.clear()
        
        if (typeof csvReader !== 'undefined' && dateString) {
            var date = new Date(dateString)
            var data = csvReader.readDataByDate(root.selectedBoiler, date)
            
            for (var i = 0; i < data.length; i++) {
                historicalDataModel.append(data[i])
            }
            
            statusText.text = "加载了 " + data.length + " 条数据记录"
        }
    }
    
    // 组件加载完成后初始化
    Component.onCompleted: {
        updateAvailableDates()
    }
}
