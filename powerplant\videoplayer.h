#ifndef VIDEOPLAY_H
#define VIDEOPLAY_H

#include <QWidget>
#include <QMediaPlayer>
#include <QVideoWidget>

namespace Ui {
class VideoPlayer;
}

class VideoPlayer : public QWidget
{
    Q_OBJECT

public:
    explicit VideoPlayer(QWidget *parent = 0);
    ~VideoPlayer();

    void setVideoShow(QString path);
    void setHttpVideoShow(QString path);

private slots:
    void updateCurrentTime(qint64 position);
    void updateTotalTime(qint64 duration);
    void handleError();

    void onSliderPressed();
    void onSliderReleased();
    void showTimeTooltip(int position);

    void switchPlayStation();


private:
    Ui::VideoPlayer *ui;
    QString formatTime(qint64 ms) const;

    QMediaPlayer *player;

    bool isPause;
    bool isUserDragging;
};

#endif // VIDEOPLAY_H
