#ifndef VIDEOMANAGER_H
#define VIDEOMANAGER_H

#include <QObject>
#include <QWidget>
#include <QQmlEngine>
#include <QMediaPlayer>
#include <QVideoWidget>

class VideoManager : public QObject
{
    Q_OBJECT
    QML_ELEMENT
    Q_PROPERTY(bool isPlaying READ isPlaying NOTIFY videoStateChanged)
    Q_PROPERTY(int currentBoiler READ currentBoiler NOTIFY currentBoilerChanged)

public:
    explicit VideoManager(QObject *parent = nullptr);
    ~VideoManager();

    // 获取视频播放器部件
    Q_INVOKABLE QWidget* getVideoWidget();

    // 视频控制方法
    Q_INVOKABLE void playVideo(int boilerNumber);
    Q_INVOKABLE void pauseVideo();
    Q_INVOKABLE void stopVideo();
    Q_INVOKABLE void togglePlayPause();
    Q_INVOKABLE void showVideoWindow();

    // 获取当前状态
    bool isPlaying() const { return m_isPlaying; }
    int currentBoiler() const { return m_currentBoiler; }

signals:
    void videoStateChanged(bool isPlaying);
    void videoError(const QString &error);
    void currentBoilerChanged(int boiler);

private slots:
    void onPlaybackStateChanged(QMediaPlayer::PlaybackState state);
    void onMediaStatusChanged(QMediaPlayer::MediaStatus status);

private:
    QMediaPlayer *m_mediaPlayer;
    QVideoWidget *m_videoWidget;
    bool m_isPlaying;
    int m_currentBoiler;
    QStringList m_videoPaths;
};

#endif // VIDEOMANAGER_H
