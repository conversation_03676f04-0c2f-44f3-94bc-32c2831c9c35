#include "datascreen.h"

// 条件编译：启用硬件数据采集
#define ENABLE_HARDWARE_DATA
#include "smoke_analyzer_comm.h"
#ifdef ENABLE_HARDWARE_DATA
#include "boiler.h"
#include "config_manager.h"
#include "dcs.h"
#endif

DataScreen::DataScreen(QObject *parent)
    : QObject(parent)
    , m_message("hello world")
    , m_isRunning(false)
    , m_timer(new QTimer(this))
    , m_isDataConnected(false)
    , m_connectionStatus("")
{
    debug_printf("数据大屏: 构造函数开始初始化\n");

    // 初始化数据为0
    m_payload = "0.0MW";
    m_mainSteamPressure = "0.00MPa";
    m_mainSteamTemp = "0.0℃";
    m_reheatSteamPressure = "0.000MPa";
    m_reheatSteamTemp = "0.0℃";
    m_mainSteamFlow = "0.0t/h";
    m_totalFuel = "0.0t/h";
    m_totalAir = "0.0t/h";
    m_oxygenContent = "0.00%";
    m_furnacePressure = "0.0Pa";
    m_coContent = "0ppm";
    m_noxContent = "0mg/Nm³";
    m_so2Content = "0ppm";

    // 初始化风机数据为0（导叶位置值，带%单位）
    m_primaryFanA = "0.0000%";
    m_primaryFanB = "0.0000%";
    m_fanA = "0.0000%";
    m_fanB = "0.0000%";
    m_inducedFanA = "0.0000%";
    m_inducedFanB = "0.0000%";

    // 设置定时器
    connect(m_timer, &QTimer::timeout, this, &DataScreen::updateData);
    // 不设置初始间隔，等待从配置文件读取

    // 加载锅炉列表
    loadBoilerList();

    // 加载DCS设备列表
    loadDcsList();

    // 设置初始定时器间隔（从配置文件读取）
    updateTimerInterval();

    debug_printf("数据大屏: 构造函数初始化完成\n");
}

QString DataScreen::message() const
{
    return m_message;
}

void DataScreen::setMessage(const QString &message)
{
    if (m_message != message) {
        m_message = message;
        emit messageChanged();
    }
}

QString DataScreen::payload() const
{
    return m_payload;
}

QString DataScreen::mainSteamPressure() const
{
    return m_mainSteamPressure;
}

QString DataScreen::mainSteamTemp() const
{
    return m_mainSteamTemp;
}

QString DataScreen::reheatSteamPressure() const
{
    return m_reheatSteamPressure;
}

QString DataScreen::reheatSteamTemp() const
{
    return m_reheatSteamTemp;
}

QString DataScreen::mainSteamFlow() const
{
    return m_mainSteamFlow;
}

QString DataScreen::totalFuel() const
{
    return m_totalFuel;
}

QString DataScreen::totalAir() const
{
    return m_totalAir;
}

QString DataScreen::oxygenContent() const
{
    return m_oxygenContent;
}

QString DataScreen::furnacePressure() const
{
    return m_furnacePressure;
}

bool DataScreen::isRunning() const
{
    return m_isRunning;
}

QStringList DataScreen::boilerList() const
{
    return m_boilerList;
}

QString DataScreen::currentBoiler() const
{
    return m_currentBoiler;
}

bool DataScreen::isDataConnected() const
{
    return m_isDataConnected;
}

QString DataScreen::connectionStatus() const
{
    return m_connectionStatus;
}

int DataScreen::getCurrentCollectionInterval() const
{
    // 获取当前锅炉和DCS的采集间隔，返回较小的值
    int boilerInterval = 15;  // 默认值
    int dcsInterval = 10;     // 默认值

    // 获取锅炉采集间隔
    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            boilerInterval = g_config_manager->get<int>(m_currentBoiler.toStdString(), "CollectionInterval", 15);
        }
    }

    // 获取DCS采集间隔
    if (!m_currentDcs.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            dcsInterval = g_config_manager->get<int>(m_currentDcs.toStdString(), "CollectionInterval", 10);
        }
    }

    // 返回较小的采集间隔，确保能及时获取到数据
    int interval = std::min(boilerInterval, dcsInterval);
    debug_printf("数据大屏采集间隔: 锅炉=%d秒, DCS=%d秒, 使用=%d秒\n",
                boilerInterval, dcsInterval, interval);
    return interval;
}

QString DataScreen::coContent() const
{
    return m_coContent;
}

QString DataScreen::noxContent() const
{
    return m_noxContent;
}

QString DataScreen::so2Content() const
{
    return m_so2Content;
}

QString DataScreen::primaryFanA() const
{
    return m_primaryFanA;
}

QString DataScreen::primaryFanB() const
{
    return m_primaryFanB;
}

QString DataScreen::fanA() const
{
    return m_fanA;
}

QString DataScreen::fanB() const
{
    return m_fanB;
}

QString DataScreen::inducedFanA() const
{
    return m_inducedFanA;
}

QString DataScreen::inducedFanB() const
{
    return m_inducedFanB;
}

QStringList DataScreen::dcsList() const
{
    return m_dcsList;
}

QString DataScreen::currentDcs() const
{
    return m_currentDcs;
}

void DataScreen::setIsRunning(bool running)
{
    if (m_isRunning != running) {
        m_isRunning = running;
        emit isRunningChanged();

        if (running) {
            startMonitoring();
        } else {
            stopMonitoring();
        }
    }
}

void DataScreen::setCurrentBoiler(const QString &boiler)
{
    if (m_currentBoiler != boiler) {
        debug_printf("数据大屏: 切换锅炉从 '%s' 到 '%s'\n",
                    m_currentBoiler.toStdString().c_str(), boiler.toStdString().c_str());

        m_currentBoiler = boiler;

        // 数据大屏不需要清理数据缓存

        // 更新定时器间隔以匹配新锅炉的采集间隔
        updateTimerInterval();

        // 立即发射信号，让UI知道设备已经改变
        emit currentBoilerChanged();

        // 重置连接状态，让下次updateData时重新检测
        m_isDataConnected = false;
        m_connectionStatus = "";
        emit dataConnectionChanged();

        // 如果监控正在运行，立即检测新设备的连接状态
        if (m_isRunning) {
            debug_printf("数据大屏: 立即检测新设备 '%s' 的连接状态\n", boiler.toStdString().c_str());
            updateData();
        }
    }
}

void DataScreen::setCurrentDcs(const QString &dcs)
{
    if (m_currentDcs != dcs) {
        debug_printf("数据大屏: 切换DCS设备从 '%s' 到 '%s'\n",
                    m_currentDcs.toStdString().c_str(), dcs.toStdString().c_str());

        m_currentDcs = dcs;

        // 更新定时器间隔以匹配新DCS设备的采集间隔
        updateTimerInterval();

        // 立即发射信号，让UI知道设备已经改变
        emit currentDcsChanged();

        // 重置连接状态，让下次updateData时重新检测
        m_isDataConnected = false;
        m_connectionStatus = "";
        emit dataConnectionChanged();

        // 如果监控正在运行，立即检测新设备的连接状态
        if (m_isRunning) {
            debug_printf("数据大屏: 立即检测新DCS设备 '%s' 的连接状态\n", dcs.toStdString().c_str());
            updateData();
        }
    }
}

void DataScreen::startMonitoring()
{
    if (!m_timer->isActive()) {
        m_timer->start();
        m_isRunning = true;
        emit isRunningChanged();

        // 初始化连接状态检查
        m_isDataConnected = false;
        m_connectionStatus = "正在检测串口数据采集设备...";
        emit dataConnectionChanged();

        // 立即进行一次数据更新和连接状态检测，不等待定时器
        debug_printf("数据大屏监控启动: 立即进行首次连接状态检测\n");
        updateData();

        // 基于配置的动态延迟更新数据
        int collectionInterval = getCurrentCollectionInterval();
        int firstDelay = collectionInterval * 1000;      // 第一次延迟：1个采集周期
        int secondDelay = collectionInterval * 1500;     // 第二次延迟：1.5个采集周期

        debug_printf("数据大屏监控: 使用动态延迟 - 第一次=%d毫秒, 第二次=%d毫秒\n",
                    firstDelay, secondDelay);

        QTimer::singleShot(firstDelay, this, [this, collectionInterval]() {
            debug_printf("数据大屏监控: 延迟%d秒后再次更新数据\n", collectionInterval);
            updateData();
            emit dataChanged();  // 强制触发数据变化信号
        });

        QTimer::singleShot(secondDelay, this, [this, collectionInterval]() {
            debug_printf("数据大屏监控: 延迟%.1f秒后第三次更新数据\n", collectionInterval * 1.5);
            updateData();
            emit dataChanged();  // 强制触发数据变化信号
        });
    }
}

void DataScreen::stopMonitoring()
{
    if (m_timer->isActive()) {
        m_timer->stop();
        m_isRunning = false;
        emit isRunningChanged();
    }
}

void DataScreen::updateData()
{
    static QDateTime lastUpdateTime = QDateTime::currentDateTime();
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeDiff = lastUpdateTime.msecsTo(currentTime);

    debug_printf("数据大屏更新调用 - 距离上次更新: %lld毫秒, 定时器间隔: %d毫秒\n",
                timeDiff, m_timer->interval());

    updateSmokeData();
    updateDcsData();

    lastUpdateTime = currentTime;
}

void DataScreen::updateSmokeData()
{
    // 检查是否有选择的烟气分析仪
    if (m_currentBoiler.isEmpty()) {
        debug_printf("数据大屏: 没有选择锅炉，跳过数据更新\n");
        return;
    }

    // 从硬件获取真实烟气数据
    float o2 = 0.0f, co = 0.0f, nox = 0.0f, so2 = 0.0f;
    float current = 0.0f, voltage = 0.0f, temperature = 0.0f;
    bool hardwareConnected = false;
    std::string deviceName = m_currentBoiler.toStdString();

    // 检查硬件连接状态
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    debug_printf("数据大屏连接状态检测: 设备名='%s', boiler_map大小=%zu\n", deviceName.c_str(), boiler_map.size());

    auto it = boiler_map.find(deviceName);
    if (it != boiler_map.end()) {
        if (it->second != nullptr) {
            debug_printf("数据大屏连接状态检测: 找到设备对象，fd=%d, is_initialized=%s\n",
                        it->second->fd, it->second->is_initialized ? "true" : "false");

            if (it->second->fd >= 0) {
                hardwareConnected = true;
                debug_printf("数据大屏连接状态检测: ✓ 硬件连接正常\n");

                // 只有在硬件连接时才获取数据
                try {
                    get_realtime_data(deviceName, &co, &o2, &nox, &so2, &current, &voltage, &temperature);
                    debug_printf("数据大屏烟气数据获取: %s - CO=%.2f, O2=%.2f, NOx=%.2f, SO2=%.2f\n",
                                deviceName.c_str(), co, o2, nox, so2);
                } catch (...) {
                    // 如果数据采集函数出现异常，设置为无效数据
                    debug_printf("数据大屏烟气数据获取异常: %s\n", deviceName.c_str());
                    o2 = co = nox = so2 = current = voltage = temperature = 0.0f;
                    hardwareConnected = false;
                }
            } else {
                debug_printf("数据大屏连接状态检测: ✗ 串口文件描述符无效: %d\n", it->second->fd);
            }
        } else {
            debug_printf("数据大屏连接状态检测: ✗ 设备对象为空指针\n");
        }
    } else {
        debug_printf("数据大屏连接状态检测: ✗ 在boiler_map中找不到设备 '%s'\n", deviceName.c_str());
    }
#endif

    // 更新连接状态和数据
    if (hardwareConnected) {
        debug_printf("数据大屏烟气数据直接使用: %s - O2=%.2f, CO=%.2f, NOx=%.2f, SO2=%.2f\n",
                    deviceName.c_str(), o2, co, nox, so2);

        // 更新连接状态
        if (!m_isDataConnected) {
            m_isDataConnected = true;
            m_connectionStatus = "";
            debug_printf("数据大屏连接状态更新: ✓ 设置为已连接状态\n");
            emit dataConnectionChanged();
        }

            // 注释：CO和O2数据现在从DCS获取，这里只更新NOx和SO2
        // m_oxygenContent 和 m_coContent 现在在updateDcsData()中更新
        m_noxContent = QString::number(nox, 'f', 0) + "mg/Nm³";
        m_so2Content = QString::number(so2, 'f', 0) + "ppm";

        debug_printf("数据大屏烟气分析仪数据更新: NOx='%s', SO2='%s'\n",
                    m_noxContent.toStdString().c_str(), m_so2Content.toStdString().c_str());

        // 数据大屏只需要实时显示当前数据，不需要存储到缓存中
        debug_printf("数据大屏烟气分析仪实时数据更新: NOx=%.1fmg/Nm³, SO2=%.0fppm\n",
                    nox, so2);

    } else {
        // 硬件未连接
        debug_printf("数据大屏连接状态检测: ✗ 硬件未连接\n");
        if (m_isDataConnected) {
            m_isDataConnected = false;
            m_connectionStatus = "";
            debug_printf("数据大屏连接状态更新: ✗ 设置为未连接状态\n");
            emit dataConnectionChanged();
        }

        // 设置烟气分析仪数据为默认值或0值（CO和O2现在由DCS提供）
        // m_oxygenContent 和 m_coContent 现在在updateDcsData()中设置
        m_noxContent = "0mg/Nm³";
        m_so2Content = "0ppm";
        // 不添加任何数据到图表数据列表
    }

    debug_printf("数据大屏: 发射dataChanged信号\n");
    emit dataChanged();
}

void DataScreen::loadBoilerList()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    m_boilerList.clear();

    // 从全局设备映射中获取烟气分析仪列表
    for (const auto& pair : boiler_map) {
        m_boilerList.append(QString::fromStdString(pair.first));
    }

    // 如果有烟气分析仪，设置第一个为默认选择
    if (!m_boilerList.isEmpty() && m_currentBoiler.isEmpty()) {
        m_currentBoiler = m_boilerList.first();
        debug_printf("数据大屏设置默认烟气分析仪: '%s'\n", m_currentBoiler.toStdString().c_str());

        // 如果监控正在运行，立即检测默认设备的连接状态
        if (m_isRunning) {
            debug_printf("数据大屏默认设备设置: 立即检测连接状态\n");
            updateData();
        }
    }

    emit boilerListChanged();

    debug_printf("数据大屏加载锅炉列表完成，共 %d 个设备\n", m_boilerList.size());
    for (const QString& boiler : m_boilerList) {
        debug_printf("  - %s\n", boiler.toStdString().c_str());
    }
#endif
}

void DataScreen::updateTimerInterval()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    extern std::unordered_map<std::string, DCSDevice*> dcs_map;
    extern ConfigManager* g_config_manager;

    if (g_config_manager == nullptr) {
        debug_printf("数据大屏: ⚠ 配置管理器未初始化，无法设置UI更新间隔\n");
        return;
    }

    // 优先使用DCS设备的采集间隔，因为数据大屏主要显示DCS数据
    if (!m_currentDcs.isEmpty()) {
        std::string dcsName = m_currentDcs.toStdString();
        debug_printf("数据大屏: 正在为DCS设备 '%s' 更新UI定时器间隔\n", dcsName.c_str());

        // 首先尝试从DCS对象获取采集间隔
        auto dcs_it = dcs_map.find(dcsName);
        if (dcs_it != dcs_map.end() && dcs_it->second != nullptr && dcs_it->second->is_initialized) {
            int collectionInterval = dcs_it->second->collection_interval;
            int uiInterval = collectionInterval * 1000; // 转换为毫秒
            m_timer->setInterval(uiInterval);
            debug_printf("数据大屏: ✓ 从DCS对象获取采集间隔: %d秒，设置UI更新间隔: %d毫秒\n", collectionInterval, uiInterval);
            debug_printf("数据大屏: ✓ 当前定时器实际间隔: %d毫秒\n", m_timer->interval());
            return;
        }

        // 如果DCS对象不可用，直接从配置文件读取DCS采集间隔
        if (g_config_manager->exists(dcsName, "CollectionInterval")) {
            int collectionInterval = g_config_manager->get<int>(dcsName, "CollectionInterval");
            if (collectionInterval > 0) {
                int uiInterval = collectionInterval * 1000; // 转换为毫秒
                m_timer->setInterval(uiInterval);
                debug_printf("数据大屏: ✓ 从配置文件获取DCS设备 '%s' 采集间隔: %d秒，设置UI更新间隔: %d毫秒\n",
                            dcsName.c_str(), collectionInterval, uiInterval);
                debug_printf("数据大屏: ✓ 当前定时器实际间隔: %d毫秒\n", m_timer->interval());
                return;
            } else {
                debug_printf("数据大屏: ⚠ DCS设备 '%s' 的采集间隔配置无效: %d秒\n", dcsName.c_str(), collectionInterval);
            }
        } else {
            debug_printf("数据大屏: ⚠ DCS设备 '%s' 的配置文件中缺少 CollectionInterval 字段\n", dcsName.c_str());
        }
    }

    // 如果没有DCS设备或DCS配置无效，回退到使用锅炉配置
    // 如果没有选择锅炉，尝试从第一个可用锅炉获取配置
    std::string boilerName;
    if (m_currentBoiler.isEmpty()) {
        if (!boiler_map.empty()) {
            boilerName = boiler_map.begin()->first;
            debug_printf("数据大屏: 回退到锅炉配置，使用第一个可用锅炉 '%s' 的配置\n", boilerName.c_str());
        } else {
            debug_printf("数据大屏: ⚠ 没有可用的锅炉配置\n");
            return;
        }
    } else {
        boilerName = m_currentBoiler.toStdString();
        debug_printf("数据大屏: 回退到锅炉 '%s' 配置更新UI定时器间隔\n", boilerName.c_str());
    }

    // 首先尝试从锅炉对象获取采集间隔
    auto it = boiler_map.find(boilerName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
        int collectionInterval = it->second->collection_interval;
        int uiInterval = collectionInterval * 1000; // 转换为毫秒
        m_timer->setInterval(uiInterval);
        debug_printf("数据大屏: ✓ 从锅炉对象获取采集间隔: %d秒，设置UI更新间隔: %d毫秒\n", collectionInterval, uiInterval);
        debug_printf("数据大屏: ✓ 当前定时器实际间隔: %d毫秒\n", m_timer->interval());
        return;
    }

    // 如果锅炉对象不可用，直接从配置文件读取
    if (g_config_manager->exists(boilerName, "CollectionInterval")) {
        int collectionInterval = g_config_manager->get<int>(boilerName, "CollectionInterval");
        if (collectionInterval > 0) {
            int uiInterval = collectionInterval * 1000; // 转换为毫秒
            m_timer->setInterval(uiInterval);
            debug_printf("数据大屏: ✓ 从配置文件获取锅炉 '%s' 采集间隔: %d秒，设置UI更新间隔: %d毫秒\n",
                        boilerName.c_str(), collectionInterval, uiInterval);
            debug_printf("数据大屏: ✓ 当前定时器实际间隔: %d毫秒\n", m_timer->interval());
            return;
        } else {
            debug_printf("数据大屏: ⚠ 锅炉 '%s' 的采集间隔配置无效: %d秒\n", boilerName.c_str(), collectionInterval);
        }
    } else {
        debug_printf("数据大屏: ⚠ 锅炉 '%s' 的配置文件中缺少 CollectionInterval 字段\n", boilerName.c_str());
    }
#endif

    debug_printf("数据大屏: ⚠ 无法获取有效的采集间隔配置，定时器未设置\n");
}

void DataScreen::loadDcsList()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, DCSDevice*> dcs_map;

    m_dcsList.clear();

    // 从全局DCS设备映射中获取设备列表
    for (const auto& pair : dcs_map) {
        m_dcsList.append(QString::fromStdString(pair.first));
    }

    // 如果有DCS设备，设置第一个为默认选择
    if (!m_dcsList.isEmpty() && m_currentDcs.isEmpty()) {
        m_currentDcs = m_dcsList.first();
        debug_printf("数据大屏设置默认DCS设备: '%s'\n", m_currentDcs.toStdString().c_str());

        // 更新定时器间隔以匹配默认DCS设备的采集间隔
        updateTimerInterval();

        // 如果监控正在运行，立即检测默认设备的连接状态
        if (m_isRunning) {
            debug_printf("数据大屏默认DCS设备设置: 立即检测连接状态\n");
            updateData();
        }
    }

    emit dcsListChanged();

    debug_printf("数据大屏加载DCS设备列表完成，共 %d 个设备\n", m_dcsList.size());
    for (const QString& dcs : m_dcsList) {
        debug_printf("  - %s\n", dcs.toStdString().c_str());
    }
#endif
}

void DataScreen::updateDcsData()
{
    // 检查是否有选择的DCS设备
    if (m_currentDcs.isEmpty()) {
        debug_printf("数据大屏: 没有选择DCS设备，设置默认值\n");
        // 设置DCS数据为默认值
        m_payload = "0.0MW";
        m_mainSteamPressure = "0.00MPa";
        m_mainSteamTemp = "0.0℃";
        m_reheatSteamPressure = "0.000MPa";
        m_reheatSteamTemp = "0.0℃";
        m_mainSteamFlow = "0.0t/h";
        m_totalFuel = "0.0t/h";
        m_totalAir = "0.0t/h";
        m_furnacePressure = "0.0Pa";
        return;
    }

    // 从硬件获取真实DCS数据
    float furnace_pressure = 0.0f, superheater_temp = 0.0f, generator_power = 0.0f;
    float main_steam_pressure = 0.0f, total_air_flow = 0.0f, water_coal_ratio = 0.0f;
    float dcs_co = 0.0f, dcs_o2 = 0.0f;
    float primary_fan_a = 0.0f, primary_fan_b = 0.0f, fan_a = 0.0f;
    float fan_b = 0.0f, induced_fan_a = 0.0f, induced_fan_b = 0.0f;
    bool dcsConnected = false;
    std::string dcsName = m_currentDcs.toStdString();

    // 检查DCS硬件连接状态
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, DCSDevice*> dcs_map;

    debug_printf("数据大屏DCS连接状态检测: 设备名='%s', dcs_map大小=%zu\n", dcsName.c_str(), dcs_map.size());

    auto it = dcs_map.find(dcsName);
    if (it != dcs_map.end()) {
        if (it->second != nullptr) {
            debug_printf("数据大屏DCS连接状态检测: 找到设备对象，fd=%d, is_initialized=%s\n",
                        it->second->fd, it->second->is_initialized ? "true" : "false");

            if (it->second->fd >= 0) {
                dcsConnected = true;
                debug_printf("数据大屏DCS连接状态检测: ✓ 硬件连接正常\n");

                // 只有在硬件连接时才获取数据
                try {
                    get_realtime_dcs_data(dcsName, &furnace_pressure, &superheater_temp, &generator_power,
                                         &main_steam_pressure, &total_air_flow, &water_coal_ratio, &dcs_co, &dcs_o2,
                                         &primary_fan_a, &primary_fan_b, &fan_a, &fan_b, &induced_fan_a, &induced_fan_b);
                    debug_printf("数据大屏DCS数据获取: %s - 炉膛压力=%.2f, 过热器温度=%.2f, 发电机功率=%.2f, 主蒸汽压力=%.2f\n",
                                dcsName.c_str(), furnace_pressure, superheater_temp, generator_power, main_steam_pressure);
                } catch (...) {
                    // 如果数据采集函数出现异常，设置为无效数据
                    debug_printf("数据大屏DCS数据获取异常: %s\n", dcsName.c_str());
                    furnace_pressure = superheater_temp = generator_power = main_steam_pressure = 0.0f;
                    total_air_flow = water_coal_ratio = dcs_co = dcs_o2 = 0.0f;
                    primary_fan_a = primary_fan_b = fan_a = fan_b = induced_fan_a = induced_fan_b = 0.0f;
                    dcsConnected = false;
                }
            } else {
                debug_printf("数据大屏DCS连接状态检测: ✗ 串口文件描述符无效: %d\n", it->second->fd);
            }
        } else {
            debug_printf("数据大屏DCS连接状态检测: ✗ 设备对象为空指针\n");
        }
    } else {
        debug_printf("数据大屏DCS连接状态检测: ✗ 在dcs_map中找不到设备 '%s'\n", dcsName.c_str());
    }
#endif

    // 更新DCS数据显示
    if (dcsConnected) {
        debug_printf("数据大屏DCS数据直接使用: %s - 炉膛压力=%.2f, 过热器温度=%.2f, 发电机功率=%.2f, 主蒸汽压力=%.2f\n",
                    dcsName.c_str(), furnace_pressure, superheater_temp, generator_power, main_steam_pressure);

        // 更新字符串格式的DCS数据（用于大屏显示）
        m_payload = QString::number(generator_power, 'f', 1) + "MW";
        m_mainSteamPressure = QString::number(main_steam_pressure, 'f', 2) + "MPa";
        m_mainSteamTemp = QString::number(superheater_temp, 'f', 1) + "℃";
        m_furnacePressure = QString::number(furnace_pressure, 'f', 1) + "Pa";
        m_totalAir = QString::number(total_air_flow, 'f', 1) + "t/h";

        // 更新CO和O2数据（从DCS获取，替代烟气分析仪数据）
        m_oxygenContent = QString::number(dcs_o2, 'f', 2) + "%";
        m_coContent = QString::number(dcs_co, 'f', 0) + "ppm";

        // 更新风机数据（导叶位置值，添加%单位）
        m_primaryFanA = QString::number(primary_fan_a, 'f', 4) + "%";
        m_primaryFanB = QString::number(primary_fan_b, 'f', 4) + "%";
        m_fanA = QString::number(fan_a, 'f', 4) + "%";
        m_fanB = QString::number(fan_b, 'f', 4) + "%";
        m_inducedFanA = QString::number(induced_fan_a, 'f', 4) + "%";
        m_inducedFanB = QString::number(induced_fan_b, 'f', 4) + "%";

        // 暂时设置为固定值的数据（这些数据在当前DCS配置中没有对应的寄存器）
        m_reheatSteamPressure = "0.000MPa";
        m_reheatSteamTemp = "0.0℃";
        m_mainSteamFlow = "0.0t/h";
        m_totalFuel = "0.0t/h";

        debug_printf("数据大屏DCS字符串数据更新: 发电机功率='%s', 主蒸汽压力='%s', 过热器温度='%s', 炉膛压力='%s'\n",
                    m_payload.toStdString().c_str(), m_mainSteamPressure.toStdString().c_str(),
                    m_mainSteamTemp.toStdString().c_str(), m_furnacePressure.toStdString().c_str());

        debug_printf("数据大屏DCS气体数据更新: O2='%s', CO='%s'\n",
                    m_oxygenContent.toStdString().c_str(), m_coContent.toStdString().c_str());

        debug_printf("数据大屏DCS风机数据更新: 一次风机A='%s', 一次风机B='%s', 送风机A='%s', 送风机B='%s', 引风机A='%s', 引风机B='%s'\n",
                    m_primaryFanA.toStdString().c_str(), m_primaryFanB.toStdString().c_str(),
                    m_fanA.toStdString().c_str(), m_fanB.toStdString().c_str(),
                    m_inducedFanA.toStdString().c_str(), m_inducedFanB.toStdString().c_str());

        // 数据大屏只需要实时显示当前数据，不需要存储到缓存中
        debug_printf("数据大屏DCS实时数据更新: 炉膛压力=%.2fPa, 过热器温度=%.2f℃, 发电机功率=%.2fMW, 主蒸汽压力=%.2fMPa\n",
                    furnace_pressure, superheater_temp, generator_power, main_steam_pressure);

        debug_printf("数据大屏DCS气体实时数据更新: O2=%.2f%%, CO=%.0fppm\n",
                    dcs_o2, dcs_co);

    } else {
        // DCS硬件未连接
        debug_printf("数据大屏DCS连接状态检测: ✗ 硬件未连接\n");

        // 设置DCS数据为默认值或0值
        m_payload = "0.0MW";
        m_mainSteamPressure = "0.00MPa";
        m_mainSteamTemp = "0.0℃";
        m_reheatSteamPressure = "0.000MPa";
        m_reheatSteamTemp = "0.0℃";
        m_mainSteamFlow = "0.0t/h";
        m_totalFuel = "0.0t/h";
        m_totalAir = "0.0t/h";
        m_furnacePressure = "0.0Pa";

        // 设置CO和O2数据为默认值（DCS未连接时）
        m_oxygenContent = "0.00%";
        m_coContent = "0ppm";

        // 设置风机数据为默认值（DCS未连接时，带%单位）
        m_primaryFanA = "0.0000%";
        m_primaryFanB = "0.0000%";
        m_fanA = "0.0000%";
        m_fanB = "0.0000%";
        m_inducedFanA = "0.0000%";
        m_inducedFanB = "0.0000%";
    }

    debug_printf("数据大屏DCS: 发射dataChanged信号\n");
}
